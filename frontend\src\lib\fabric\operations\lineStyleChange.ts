import type { Canvas } from "fabric";

/*
Converts line style string to Fabric.js strokeDashArray format.
*/
const getStrokeDashArray = (lineStyle: string): number[] | undefined => {
  switch (lineStyle) {
    case "solid":
      return undefined; // No dash array for solid lines
    case "dashed":
      return [10, 5]; // Long dashes with short gaps
    case "dotted":
      return [2, 3]; // Short dots with small gaps
    default:
      return undefined;
  }
};

/*
Updates the line style of selected objects.
Only applies to objects that have stroke (excludes textbox, cropRect, protractor, highlight).
*/
export const changeSelectedObjectsLineStyle = (canvas: Canvas, lineStyle: string): void => {
  const selectedObject = canvas.getActiveObject();
  if (!selectedObject) return;

  const selectedObjects =
    selectedObject.type === "activeselection"
      ? (selectedObject as any).getObjects()
      : [selectedObject];

  const strokeDashArray = getStrokeDashArray(lineStyle);

  selectedObjects.forEach((obj: any) => {
    // Only apply to objects that have stroke (same exclusions as stroke width)
    if (
      obj.type !== "textbox" &&
      obj.name !== "cropRect" &&
      obj.name !== "protractor" &&
      obj.name !== "highlight"
    ) {
      obj.set({ strokeDashArray });
    }
  });

  canvas.renderAll();
};

/*
Gets the current line style from a selected object.
Returns the line style string based on strokeDashArray, undefined if object doesn't have stroke.
*/
export const getObjectLineStyle = (obj: any): string | undefined => {
  if (!obj) return undefined;

  // Exclude objects that don't use line style
  if (
    obj.type === "textbox" ||
    obj.name === "cropRect" ||
    obj.name === "protractor" ||
    obj.name === "highlight"
  ) {
    return undefined;
  }

  const strokeDashArray = obj.strokeDashArray;
  
  if (!strokeDashArray || strokeDashArray.length === 0) {
    return "solid";
  }
  
  // Check for dashed pattern (longer dashes)
  if (strokeDashArray.length >= 2 && strokeDashArray[0] >= 8) {
    return "dashed";
  }
  
  // Check for dotted pattern (shorter dashes)
  if (strokeDashArray.length >= 2 && strokeDashArray[0] <= 3) {
    return "dotted";
  }
  
  return "solid";
};

/*
Context-aware line style change handler.
If object is selected: only update the selected object.
If nothing is selected: call the default change handler.
*/
export const applyLineStyleContextAware = (
  canvas: Canvas,
  lineStyle: string,
  onDefaultChange?: (lineStyle: string) => void
): void => {
  const selectedObject = canvas.getActiveObject();
  
  if (selectedObject) {
    changeSelectedObjectsLineStyle(canvas, lineStyle);
  } else {
    onDefaultChange?.(lineStyle);
  }
};
