function bi(l,y){const{rows:m,columns:$,data:A}=l,{rows:r,columns:H,data:I}=y,W=[],q=[],L=[];for(let N=0;N<H;N++){const M=N*($-1)/(H-1);W[N]=Math.floor(M),q[N]=Math.min(W[N]+1,$-1),L[N]=M-W[N]}for(let N=0;N<r;N++){const M=N*(m-1)/(r-1),X=Math.floor(M)*$,z=Math.min(X+$,(m-1)*$),ee=M-Math.floor(M),fe=1-ee,Ae=N*H;for(let pe=0;pe<H;pe++){const we=A[X+W[pe]],ye=A[X+q[pe]],se=A[z+W[pe]],_e=A[z+q[pe]],$e=1-L[pe];I[Ae+pe]=(we*$e+ye*L[pe])*fe+(se*$e+_e*L[pe])*ee}}return I}function Pi(l,y){const{rows:m,columns:$,pixelData:A,samplesPerPixel:r=1}=l,{rows:H,columns:I,pixelData:W}=y,q=[];for(let L=0;L<I;L++){const N=L*($-1)/(I-1);q[L]=Math.floor(N)*r}for(let L=0;L<H;L++){const N=L*(m-1)/(H-1),M=Math.floor(N)*$*r,X=L*I;for(let z=0;z<I;z++)for(let ee=0;ee<r;ee++)W[X+z+ee]=A[M+q[z]+ee]}return W}/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const di=Symbol("Comlink.proxy"),Ti=Symbol("Comlink.endpoint"),Ci=Symbol("Comlink.releaseProxy"),qn=Symbol("Comlink.finalizer"),Mn=Symbol("Comlink.thrown"),pi=l=>typeof l=="object"&&l!==null||typeof l=="function",Ai={canHandle:l=>pi(l)&&l[di],serialize(l){const{port1:y,port2:m}=new MessageChannel;return Zn(l,y),[m,[m]]},deserialize(l){return l.start(),Si(l)}},$i={canHandle:l=>pi(l)&&Mn in l,serialize({value:l}){let y;return l instanceof Error?y={isError:!0,value:{message:l.message,name:l.name,stack:l.stack}}:y={isError:!1,value:l},[y,[]]},deserialize(l){throw l.isError?Object.assign(new Error(l.value.message),l.value):l.value}},vi=new Map([["proxy",Ai],["throw",$i]]);function Ri(l,y){for(const m of l)if(y===m||m==="*"||m instanceof RegExp&&m.test(y))return!0;return!1}function Zn(l,y=globalThis,m=["*"]){y.addEventListener("message",function $(A){if(!A||!A.data)return;if(!Ri(m,A.origin)){console.warn(`Invalid origin '${A.origin}' for comlink proxy`);return}const{id:r,type:H,path:I}=Object.assign({path:[]},A.data),W=(A.data.argumentList||[]).map(kn);let q;try{const L=I.slice(0,-1).reduce((M,X)=>M[X],l),N=I.reduce((M,X)=>M[X],l);switch(H){case"GET":q=N;break;case"SET":L[I.slice(-1)[0]]=kn(A.data.value),q=!0;break;case"APPLY":q=N.apply(L,W);break;case"CONSTRUCT":{const M=new N(...W);q=Ui(M)}break;case"ENDPOINT":{const{port1:M,port2:X}=new MessageChannel;Zn(l,X),q=Fi(M,[M])}break;case"RELEASE":q=void 0;break;default:return}}catch(L){q={value:L,[Mn]:0}}Promise.resolve(q).catch(L=>({value:L,[Mn]:0})).then(L=>{const[N,M]=Nn(L);y.postMessage(Object.assign(Object.assign({},N),{id:r}),M),H==="RELEASE"&&(y.removeEventListener("message",$),hi(y),qn in l&&typeof l[qn]=="function"&&l[qn]())}).catch(L=>{const[N,M]=Nn({value:new TypeError("Unserializable return value"),[Mn]:0});y.postMessage(Object.assign(Object.assign({},N),{id:r}),M)})}),y.start&&y.start()}function Ei(l){return l.constructor.name==="MessagePort"}function hi(l){Ei(l)&&l.close()}function Si(l,y){return Kn(l,[],y)}function Hn(l){if(l)throw new Error("Proxy has been released and is not useable")}function gi(l){return Dn(l,{type:"RELEASE"}).then(()=>{hi(l)})}const Vn=new WeakMap,Bn="FinalizationRegistry"in globalThis&&new FinalizationRegistry(l=>{const y=(Vn.get(l)||0)-1;Vn.set(l,y),y===0&&gi(l)});function ki(l,y){const m=(Vn.get(y)||0)+1;Vn.set(y,m),Bn&&Bn.register(l,y,l)}function Di(l){Bn&&Bn.unregister(l)}function Kn(l,y=[],m=function(){}){let $=!1;const A=new Proxy(m,{get(r,H){if(Hn($),H===Ci)return()=>{Di(A),gi(l),$=!0};if(H==="then"){if(y.length===0)return{then:()=>A};const I=Dn(l,{type:"GET",path:y.map(W=>W.toString())}).then(kn);return I.then.bind(I)}return Kn(l,[...y,H])},set(r,H,I){Hn($);const[W,q]=Nn(I);return Dn(l,{type:"SET",path:[...y,H].map(L=>L.toString()),value:W},q).then(kn)},apply(r,H,I){Hn($);const W=y[y.length-1];if(W===Ti)return Dn(l,{type:"ENDPOINT"}).then(kn);if(W==="bind")return Kn(l,y.slice(0,-1));const[q,L]=ri(I);return Dn(l,{type:"APPLY",path:y.map(N=>N.toString()),argumentList:q},L).then(kn)},construct(r,H){Hn($);const[I,W]=ri(H);return Dn(l,{type:"CONSTRUCT",path:y.map(q=>q.toString()),argumentList:I},W).then(kn)}});return ki(A,l),A}function Oi(l){return Array.prototype.concat.apply([],l)}function ri(l){const y=l.map(Nn);return[y.map(m=>m[0]),Oi(y.map(m=>m[1]))]}const yi=new WeakMap;function Fi(l,y){return yi.set(l,y),l}function Ui(l){return Object.assign(l,{[di]:!0})}function Nn(l){for(const[y,m]of vi)if(m.canHandle(l)){const[$,A]=m.serialize(l);return[{type:"HANDLER",name:y,value:$},A]}return[{type:"RAW",value:l},yi.get(l)||[]]}function kn(l){switch(l.type){case"HANDLER":return vi.get(l.name).deserialize(l.value);case"RAW":return l.value}}function Dn(l,y,m){return new Promise($=>{const A=Ii();l.addEventListener("message",function r(H){!H.data||!H.data.id||H.data.id!==A||(l.removeEventListener("message",r),$(H.data))}),l.start&&l.start(),l.postMessage(Object.assign({id:A},y),m)})}function Ii(){return new Array(4).fill(0).map(()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16)).join("-")}async function ni(l,y){let m=y.buffer,$=y.byteOffset;const A=y.length;if(l.bitsAllocated===16)$%2&&(m=m.slice($),$=0),l.pixelRepresentation===0?l.pixelData=new Uint16Array(m,$,A/2):l.pixelData=new Int16Array(m,$,A/2);else if(l.bitsAllocated===8||l.bitsAllocated===1)l.pixelData=y;else if(l.bitsAllocated===32){if($%2&&(m=m.slice($),$=0),l.floatPixelData||l.doubleFloatPixelData)throw new Error("Float pixel data is not supported for parsing into ImageFrame");l.pixelRepresentation===0?l.pixelData=new Uint32Array(m,$,A/4):l.pixelRepresentation===1?l.pixelData=new Int32Array(m,$,A/4):l.pixelData=new Float32Array(m,$,A/4)}return l}function ji(l){return(l&255)<<8|l>>8&255}async function xi(l,y){if(l.bitsAllocated===16){let m=y.buffer,$=y.byteOffset;const A=y.length;$%2&&(m=m.slice($),$=0),l.pixelRepresentation===0?l.pixelData=new Uint16Array(m,$,A/2):l.pixelData=new Int16Array(m,$,A/2);for(let r=0;r<l.pixelData.length;r++)l.pixelData[r]=ji(l.pixelData[r])}else l.bitsAllocated===8&&(l.pixelData=y);return l}async function Wi(l,y){if(l.bitsAllocated===8)return l.planarConfiguration?Mi(l,y):Hi(l,y);if(l.bitsAllocated===16)return Li(l,y);throw new Error("unsupported pixel format for RLE")}function Hi(l,y){const m=y,$=l.rows*l.columns,A=new ArrayBuffer($*l.samplesPerPixel),r=new DataView(m.buffer,m.byteOffset),H=new Int8Array(m.buffer,m.byteOffset),I=new Int8Array(A);let W=0;const q=r.getInt32(0,!0);for(let L=0;L<q;++L){W=L;let N=r.getInt32((L+1)*4,!0),M=r.getInt32((L+2)*4,!0);M===0&&(M=m.length);const X=$*q;for(;N<M;){const z=H[N++];if(z>=0&&z<=127)for(let ee=0;ee<z+1&&W<X;++ee)I[W]=H[N++],W+=l.samplesPerPixel;else if(z<=-1&&z>=-127){const ee=H[N++];for(let fe=0;fe<-z+1&&W<X;++fe)I[W]=ee,W+=l.samplesPerPixel}}}return l.pixelData=new Uint8Array(A),l}function Mi(l,y){const m=y,$=l.rows*l.columns,A=new ArrayBuffer($*l.samplesPerPixel),r=new DataView(m.buffer,m.byteOffset),H=new Int8Array(m.buffer,m.byteOffset),I=new Int8Array(A);let W=0;const q=r.getInt32(0,!0);for(let L=0;L<q;++L){W=L*$;let N=r.getInt32((L+1)*4,!0),M=r.getInt32((L+2)*4,!0);M===0&&(M=m.length);const X=$*q;for(;N<M;){const z=H[N++];if(z>=0&&z<=127)for(let ee=0;ee<z+1&&W<X;++ee)I[W]=H[N++],W++;else if(z<=-1&&z>=-127){const ee=H[N++];for(let fe=0;fe<-z+1&&W<X;++fe)I[W]=ee,W++}}}return l.pixelData=new Uint8Array(A),l}function Li(l,y){const m=y,$=l.rows*l.columns,A=new ArrayBuffer($*l.samplesPerPixel*2),r=new DataView(m.buffer,m.byteOffset),H=new Int8Array(m.buffer,m.byteOffset),I=new Int8Array(A),W=r.getInt32(0,!0);for(let q=0;q<W;++q){let L=0;const N=q===0?1:0;let M=r.getInt32((q+1)*4,!0),X=r.getInt32((q+2)*4,!0);for(X===0&&(X=m.length);M<X;){const z=H[M++];if(z>=0&&z<=127)for(let ee=0;ee<z+1&&L<$;++ee)I[L*2+N]=H[M++],L++;else if(z<=-1&&z>=-127){const ee=H[M++];for(let fe=0;fe<-z+1&&L<$;++fe)I[L*2+N]=ee,L++}}}return l.pixelRepresentation===0?l.pixelData=new Uint16Array(A):l.pixelData=new Int16Array(A),l}function zn(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}function Vi(l){if(Object.prototype.hasOwnProperty.call(l,"__esModule"))return l;var y=l.default;if(typeof y=="function"){var m=function $(){return this instanceof $?Reflect.construct(y,arguments,this.constructor):y.apply(this,arguments)};m.prototype=y.prototype}else m={};return Object.defineProperty(m,"__esModule",{value:!0}),Object.keys(l).forEach(function($){var A=Object.getOwnPropertyDescriptor(l,$);Object.defineProperty(m,$,A.get?A:{enumerable:!0,get:function(){return l[$]}})}),m}var Gn={exports:{}},Bi={},Ni=Object.freeze({__proto__:null,default:Bi}),Qr=Vi(Ni),ii;function zi(){return ii||(ii=1,function(l,y){var m=(()=>{var $=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<"u"&&($=$||__filename),function(A){A=A||{};var r=typeof A<"u"?A:{},H,I;r.ready=new Promise(function(e,t){H=e,I=t});var W=Object.assign({},r),q="./this.program",L=(e,t)=>{throw t},N=typeof window=="object",M=typeof importScripts=="function",X=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",z="";function ee(e){return r.locateFile?r.locateFile(e,z):z+e}var fe,Ae,pe;if(X){var we=Qr,ye=Qr;M?z=ye.dirname(z)+"/":z=__dirname+"/",fe=(e,t)=>(e=Te(e)?new URL(e):ye.normalize(e),we.readFileSync(e,t?void 0:"utf8")),pe=e=>{var t=fe(e,!0);return t.buffer||(t=new Uint8Array(t)),t},Ae=(e,t,o)=>{e=Te(e)?new URL(e):ye.normalize(e),we.readFile(e,function(c,p){c?o(c):t(p.buffer)})},process.argv.length>1&&(q=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",function(e){if(!(e instanceof Gt))throw e}),process.on("unhandledRejection",function(e){throw e}),L=(e,t)=>{throw process.exitCode=e,t},r.inspect=function(){return"[Emscripten Module object]"}}else(N||M)&&(M?z=self.location.href:typeof document<"u"&&document.currentScript&&(z=document.currentScript.src),$&&(z=$),z.indexOf("blob:")!==0?z=z.substr(0,z.replace(/[?#].*/,"").lastIndexOf("/")+1):z="",fe=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},M&&(pe=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),Ae=(e,t,o)=>{var c=new XMLHttpRequest;c.open("GET",e,!0),c.responseType="arraybuffer",c.onload=()=>{if(c.status==200||c.status==0&&c.response){t(c.response);return}o()},c.onerror=o,c.send(null)});var se=r.print||console.log.bind(console),_e=r.printErr||console.warn.bind(console);Object.assign(r,W),W=null,r.arguments&&r.arguments,r.thisProgram&&(q=r.thisProgram),r.quit&&(L=r.quit);var $e;r.wasmBinary&&($e=r.wasmBinary),r.noExitRuntime,typeof WebAssembly!="object"&&ut("no native wasm support detected");var at,Lt=!1;function St(e,t){e||ut(t)}var tr=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function rr(e,t,o){for(var c=t+o,p=t;e[p]&&!(p>=c);)++p;if(p-t>16&&e.buffer&&tr)return tr.decode(e.subarray(t,p));for(var v="";t<p;){var g=e[t++];if(!(g&128)){v+=String.fromCharCode(g);continue}var _=e[t++]&63;if((g&224)==192){v+=String.fromCharCode((g&31)<<6|_);continue}var P=e[t++]&63;if((g&240)==224?g=(g&15)<<12|_<<6|P:g=(g&7)<<18|_<<12|P<<6|e[t++]&63,g<65536)v+=String.fromCharCode(g);else{var S=g-65536;v+=String.fromCharCode(55296|S>>10,56320|S&1023)}}return v}function hr(e,t){return e?rr(ue,e,t):""}function nr(e,t,o,c){if(!(c>0))return 0;for(var p=o,v=o+c-1,g=0;g<e.length;++g){var _=e.charCodeAt(g);if(_>=55296&&_<=57343){var P=e.charCodeAt(++g);_=65536+((_&1023)<<10)|P&1023}if(_<=127){if(o>=v)break;t[o++]=_}else if(_<=2047){if(o+1>=v)break;t[o++]=192|_>>6,t[o++]=128|_&63}else if(_<=65535){if(o+2>=v)break;t[o++]=224|_>>12,t[o++]=128|_>>6&63,t[o++]=128|_&63}else{if(o+3>=v)break;t[o++]=240|_>>18,t[o++]=128|_>>12&63,t[o++]=128|_>>6&63,t[o++]=128|_&63}}return t[o]=0,o-p}function Xe(e,t,o){return nr(e,ue,t,o)}function me(e){for(var t=0,o=0;o<e.length;++o){var c=e.charCodeAt(o);c<=127?t++:c<=2047?t+=2:c>=55296&&c<=57343?(t+=4,++o):t+=3}return t}var le,ce,ue,ne,oe,ie,he,Vt,Bt;function Nt(e){le=e,r.HEAP8=ce=new Int8Array(e),r.HEAP16=ne=new Int16Array(e),r.HEAP32=ie=new Int32Array(e),r.HEAPU8=ue=new Uint8Array(e),r.HEAPU16=oe=new Uint16Array(e),r.HEAPU32=he=new Uint32Array(e),r.HEAPF32=Vt=new Float32Array(e),r.HEAPF64=Bt=new Float64Array(e)}r.INITIAL_MEMORY;var zt,ir=[],gr=[],Or=[];function Zr(){if(r.preRun)for(typeof r.preRun=="function"&&(r.preRun=[r.preRun]);r.preRun.length;)Ct(r.preRun.shift());Dt(ir)}function en(){Dt(gr)}function tn(){if(r.postRun)for(typeof r.postRun=="function"&&(r.postRun=[r.postRun]);r.postRun.length;)Ke(r.postRun.shift());Dt(Or)}function Ct(e){ir.unshift(e)}function Ye(e){gr.unshift(e)}function Ke(e){Or.unshift(e)}var Qe=0,st=null;function qt(e){Qe++,r.monitorRunDependencies&&r.monitorRunDependencies(Qe)}function kt(e){if(Qe--,r.monitorRunDependencies&&r.monitorRunDependencies(Qe),Qe==0&&st){var t=st;st=null,t()}}function ut(e){r.onAbort&&r.onAbort(e),e="Aborted("+e+")",_e(e),Lt=!0,e+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(e);throw I(t),t}var Pe="data:application/octet-stream;base64,";function be(e){return e.startsWith(Pe)}function Te(e){return e.startsWith("file://")}var De;De="libjpegturbowasm_decode.wasm",be(De)||(De=ee(De));function Fr(e){try{if(e==De&&$e)return new Uint8Array($e);if(pe)return pe(e);throw"both async and sync fetching of the wasm failed"}catch(t){ut(t)}}function yr(){if(!$e&&(N||M)){if(typeof fetch=="function"&&!Te(De))return fetch(De,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+De+"'";return e.arrayBuffer()}).catch(function(){return Fr(De)});if(Ae)return new Promise(function(e,t){Ae(De,function(o){e(new Uint8Array(o))},t)})}return Promise.resolve().then(function(){return Fr(De)})}function _r(){var e={a:R};function t(g,_){var P=g.exports;r.asm=P,at=r.asm.K,Nt(at.buffer),zt=r.asm.M,Ye(r.asm.L),kt()}qt();function o(g){t(g.instance)}function c(g){return yr().then(function(_){return WebAssembly.instantiate(_,e)}).then(function(_){return _}).then(g,function(_){_e("failed to asynchronously prepare wasm: "+_),ut(_)})}function p(){return!$e&&typeof WebAssembly.instantiateStreaming=="function"&&!be(De)&&!Te(De)&&!X&&typeof fetch=="function"?fetch(De,{credentials:"same-origin"}).then(function(g){var _=WebAssembly.instantiateStreaming(g,e);return _.then(o,function(P){return _e("wasm streaming compile failed: "+P),_e("falling back to ArrayBuffer instantiation"),c(o)})}):c(o)}if(r.instantiateWasm)try{var v=r.instantiateWasm(e,t);return v}catch(g){_e("Module.instantiateWasm callback failed with error: "+g),I(g)}return p().catch(I),{}}function Gt(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function Dt(e){for(;e.length>0;)e.shift()(r)}function Ur(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(t){he[this.ptr+4>>2]=t},this.get_type=function(){return he[this.ptr+4>>2]},this.set_destructor=function(t){he[this.ptr+8>>2]=t},this.get_destructor=function(){return he[this.ptr+8>>2]},this.set_refcount=function(t){ie[this.ptr>>2]=t},this.set_caught=function(t){t=t?1:0,ce[this.ptr+12>>0]=t},this.get_caught=function(){return ce[this.ptr+12>>0]!=0},this.set_rethrown=function(t){t=t?1:0,ce[this.ptr+13>>0]=t},this.get_rethrown=function(){return ce[this.ptr+13>>0]!=0},this.init=function(t,o){this.set_adjusted_ptr(0),this.set_type(t),this.set_destructor(o),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var t=ie[this.ptr>>2];ie[this.ptr>>2]=t+1},this.release_ref=function(){var t=ie[this.ptr>>2];return ie[this.ptr>>2]=t-1,t===1},this.set_adjusted_ptr=function(t){he[this.ptr+16>>2]=t},this.get_adjusted_ptr=function(){return he[this.ptr+16>>2]},this.get_exception_ptr=function(){var t=d(this.get_type());if(t)return he[this.excPtr>>2];var o=this.get_adjusted_ptr();return o!==0?o:this.excPtr}}function Me(e,t,o){var c=new Ur(e);throw c.init(t,o),e}var Le={};function ge(e){for(;e.length;){var t=e.pop(),o=e.pop();o(t)}}function Ve(e){return this.fromWireType(ie[e>>2])}var ze={},Be={},Ot={},mr=48,Jt=57;function Ft(e){if(e===void 0)return"_unknown";e=e.replace(/[^a-zA-Z0-9_]/g,"$");var t=e.charCodeAt(0);return t>=mr&&t<=Jt?"_"+e:e}function Ze(e,t){return e=Ft(e),new Function("body","return function "+e+`() {
    "use strict";    return body.apply(this, arguments);
};
`)(t)}function et(e,t){var o=Ze(t,function(c){this.name=t,this.message=c;var p=new Error(c).stack;p!==void 0&&(this.stack=this.toString()+`
`+p.replace(/^Error(:[^\n]*)?\n/,""))});return o.prototype=Object.create(e.prototype),o.prototype.constructor=o,o.prototype.toString=function(){return this.message===void 0?this.name:this.name+": "+this.message},o}var ft=void 0;function Ue(e){throw new ft(e)}function Ie(e,t,o){e.forEach(function(_){Ot[_]=t});function c(_){var P=o(_);P.length!==e.length&&Ue("Mismatched type converter count");for(var S=0;S<e.length;++S)Oe(e[S],P[S])}var p=new Array(t.length),v=[],g=0;t.forEach((_,P)=>{Be.hasOwnProperty(_)?p[P]=Be[_]:(v.push(_),ze.hasOwnProperty(_)||(ze[_]=[]),ze[_].push(()=>{p[P]=Be[_],++g,g===v.length&&c(p)}))}),v.length===0&&c(p)}function or(e){var t=Le[e];delete Le[e];var o=t.rawConstructor,c=t.rawDestructor,p=t.fields,v=p.map(g=>g.getterReturnType).concat(p.map(g=>g.setterArgumentType));Ie([e],v,g=>{var _={};return p.forEach((P,S)=>{var j=P.fieldName,B=g[S],V=P.getter,te=P.getterContext,Z=g[S+p.length],ve=P.setter,Fe=P.setterContext;_[j]={read:He=>B.fromWireType(V(te,He)),write:(He,Sn)=>{var Mt=[];ve(Fe,He,Z.toWireType(Mt,Sn)),ge(Mt)}}}),[{name:t.name,fromWireType:function(P){var S={};for(var j in _)S[j]=_[j].read(P);return c(P),S},toWireType:function(P,S){for(var j in _)if(!(j in S))throw new TypeError('Missing field:  "'+j+'"');var B=o();for(j in _)_[j].write(B,S[j]);return P!==null&&P.push(c,B),B},argPackAdvance:8,readValueFromPointer:Ve,destructorFunction:c}]})}function ar(e,t,o,c,p){}function Ce(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}function ct(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);Y=e}var Y=void 0;function de(e){for(var t="",o=e;ue[o];)t+=Y[ue[o++]];return t}var re=void 0;function Q(e){throw new re(e)}function Oe(e,t,o={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var c=t.name;if(e||Q('type "'+c+'" must have a positive integer typeid pointer'),Be.hasOwnProperty(e)){if(o.ignoreDuplicateRegistrations)return;Q("Cannot register type '"+c+"' twice")}if(Be[e]=t,delete Ot[e],ze.hasOwnProperty(e)){var p=ze[e];delete ze[e],p.forEach(v=>v())}}function wr(e,t,o,c,p){var v=Ce(o);t=de(t),Oe(e,{name:t,fromWireType:function(g){return!!g},toWireType:function(g,_){return _?c:p},argPackAdvance:8,readValueFromPointer:function(g){var _;if(o===1)_=ce;else if(o===2)_=ne;else if(o===4)_=ie;else throw new TypeError("Unknown boolean type size: "+t);return this.fromWireType(_[g>>v])},destructorFunction:null})}function br(e){if(!(this instanceof yt)||!(e instanceof yt))return!1;for(var t=this.$$.ptrType.registeredClass,o=this.$$.ptr,c=e.$$.ptrType.registeredClass,p=e.$$.ptr;t.baseClass;)o=t.upcast(o),t=t.baseClass;for(;c.baseClass;)p=c.upcast(p),c=c.baseClass;return t===c&&o===p}function Ut(e){return{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}function Xt(e){function t(o){return o.$$.ptrType.registeredClass.name}Q(t(e)+" instance already deleted")}var It=!1;function Re(e){}function mt(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}function ae(e){e.count.value-=1;var t=e.count.value===0;t&&mt(e)}function tt(e,t,o){if(t===o)return e;if(o.baseClass===void 0)return null;var c=tt(e,t,o.baseClass);return c===null?null:o.downcast(c)}var jt={};function Pr(){return Object.keys(vt).length}function lt(){var e=[];for(var t in vt)vt.hasOwnProperty(t)&&e.push(vt[t]);return e}var dt=[];function pt(){for(;dt.length;){var e=dt.pop();e.$$.deleteScheduled=!1,e.delete()}}var nt=void 0;function rn(e){nt=e,dt.length&&nt&&nt(pt)}function xt(){r.getInheritedInstanceCount=Pr,r.getLiveInheritedInstances=lt,r.flushPendingDeletes=pt,r.setDelayFunction=rn}var vt={};function Ir(e,t){for(t===void 0&&Q("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}function wt(e,t){return t=Ir(e,t),vt[t]}function sr(e,t){(!t.ptrType||!t.ptr)&&Ue("makeClassHandle requires ptr and ptrType");var o=!!t.smartPtrType,c=!!t.smartPtr;return o!==c&&Ue("Both smartPtrType and smartPtr must be specified"),t.count={value:1},At(Object.create(e,{$$:{value:t}}))}function ht(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var o=wt(this.registeredClass,t);if(o!==void 0){if(o.$$.count.value===0)return o.$$.ptr=t,o.$$.smartPtr=e,o.clone();var c=o.clone();return this.destructor(e),c}function p(){return this.isSmartPointer?sr(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):sr(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var v=this.registeredClass.getActualType(t),g=jt[v];if(!g)return p.call(this);var _;this.isConst?_=g.constPointerType:_=g.pointerType;var P=tt(t,this.registeredClass,_.registeredClass);return P===null?p.call(this):this.isSmartPointer?sr(_.registeredClass.instancePrototype,{ptrType:_,ptr:P,smartPtrType:this,smartPtr:e}):sr(_.registeredClass.instancePrototype,{ptrType:_,ptr:P})}function At(e){return typeof FinalizationRegistry>"u"?(At=t=>t,e):(It=new FinalizationRegistry(t=>{ae(t.$$)}),At=t=>{var o=t.$$,c=!!o.smartPtr;if(c){var p={$$:o};It.register(t,p,t)}return t},Re=t=>It.unregister(t),At(e))}function Yt(){if(this.$$.ptr||Xt(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=At(Object.create(Object.getPrototypeOf(this),{$$:{value:Ut(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e}function nn(){this.$$.ptr||Xt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Q("Object already scheduled for deletion"),Re(this),ae(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function bt(){return!this.$$.ptr}function Wt(){return this.$$.ptr||Xt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Q("Object already scheduled for deletion"),dt.push(this),dt.length===1&&nt&&nt(pt),this.$$.deleteScheduled=!0,this}function gt(){yt.prototype.isAliasOf=br,yt.prototype.clone=Yt,yt.prototype.delete=nn,yt.prototype.isDeleted=bt,yt.prototype.deleteLater=Wt}function yt(){}function $t(e,t,o){if(e[t].overloadTable===void 0){var c=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||Q("Function '"+o+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[c.argCount]=c}}function on(e,t,o){r.hasOwnProperty(e)?(Q("Cannot register public name '"+e+"' twice"),$t(r,e,e),r.hasOwnProperty(o)&&Q("Cannot register multiple overloads of a function with the same number of arguments ("+o+")!"),r[e].overloadTable[o]=t):r[e]=t}function Rt(e,t,o,c,p,v,g,_){this.name=e,this.constructor=t,this.instancePrototype=o,this.rawDestructor=c,this.baseClass=p,this.getActualType=v,this.upcast=g,this.downcast=_,this.pureVirtualFunctions=[]}function Tr(e,t,o){for(;t!==o;)t.upcast||Q("Expected null or instance of "+o.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function an(e,t){if(t===null)return this.isReference&&Q("null is not a valid "+this.name),0;t.$$||Q('Cannot pass "'+Ne(t)+'" as a '+this.name),t.$$.ptr||Q("Cannot pass deleted object as a pointer of type "+this.name);var o=t.$$.ptrType.registeredClass,c=Tr(t.$$.ptr,o,this.registeredClass);return c}function sn(e,t){var o;if(t===null)return this.isReference&&Q("null is not a valid "+this.name),this.isSmartPointer?(o=this.rawConstructor(),e!==null&&e.push(this.rawDestructor,o),o):0;t.$$||Q('Cannot pass "'+Ne(t)+'" as a '+this.name),t.$$.ptr||Q("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&Q("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var c=t.$$.ptrType.registeredClass;if(o=Tr(t.$$.ptr,c,this.registeredClass),this.isSmartPointer)switch(t.$$.smartPtr===void 0&&Q("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?o=t.$$.smartPtr:Q("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:o=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)o=t.$$.smartPtr;else{var p=t.clone();o=this.rawShare(o,Tt.toHandle(function(){p.delete()})),e!==null&&e.push(this.rawDestructor,o)}break;default:Q("Unsupporting sharing policy")}return o}function un(e,t){if(t===null)return this.isReference&&Q("null is not a valid "+this.name),0;t.$$||Q('Cannot pass "'+Ne(t)+'" as a '+this.name),t.$$.ptr||Q("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&Q("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var o=t.$$.ptrType.registeredClass,c=Tr(t.$$.ptr,o,this.registeredClass);return c}function fn(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function Se(e){this.rawDestructor&&this.rawDestructor(e)}function Cr(e){e!==null&&e.delete()}function it(){qe.prototype.getPointee=fn,qe.prototype.destructor=Se,qe.prototype.argPackAdvance=8,qe.prototype.readValueFromPointer=Ve,qe.prototype.deleteObject=Cr,qe.prototype.fromWireType=ht}function qe(e,t,o,c,p,v,g,_,P,S,j){this.name=e,this.registeredClass=t,this.isReference=o,this.isConst=c,this.isSmartPointer=p,this.pointeeType=v,this.sharingPolicy=g,this.rawGetPointee=_,this.rawConstructor=P,this.rawShare=S,this.rawDestructor=j,!p&&t.baseClass===void 0?c?(this.toWireType=an,this.destructorFunction=null):(this.toWireType=un,this.destructorFunction=null):this.toWireType=sn}function ur(e,t,o){r.hasOwnProperty(e)||Ue("Replacing nonexistant public symbol"),r[e].overloadTable!==void 0&&o!==void 0||(r[e]=t,r[e].argCount=o)}function fr(e,t,o){var c=r["dynCall_"+e];return o&&o.length?c.apply(null,[t].concat(o)):c.call(null,t)}var Kt=[];function Ee(e){var t=Kt[e];return t||(e>=Kt.length&&(Kt.length=e+1),Kt[e]=t=zt.get(e)),t}function jr(e,t,o){if(e.includes("j"))return fr(e,t,o);var c=Ee(t).apply(null,o);return c}function rt(e,t){var o=[];return function(){return o.length=0,Object.assign(o,arguments),jr(e,t,o)}}function je(e,t){e=de(e);function o(){return e.includes("j")?rt(e,t):Ee(t)}var c=o();return typeof c!="function"&&Q("unknown function pointer with signature "+e+": "+t),c}var Ar=void 0;function xe(e){var t=G(e),o=de(t);return x(t),o}function cr(e,t){var o=[],c={};function p(v){if(!c[v]&&!Be[v]){if(Ot[v]){Ot[v].forEach(p);return}o.push(v),c[v]=!0}}throw t.forEach(p),new Ar(e+": "+o.map(xe).join([", "]))}function lr(e,t,o,c,p,v,g,_,P,S,j,B,V){j=de(j),v=je(p,v),_&&(_=je(g,_)),S&&(S=je(P,S)),V=je(B,V);var te=Ft(j);on(te,function(){cr("Cannot construct "+j+" due to unbound types",[c])}),Ie([e,t,o],c?[c]:[],function(Z){Z=Z[0];var ve,Fe;c?(ve=Z.registeredClass,Fe=ve.instancePrototype):Fe=yt.prototype;var He=Ze(te,function(){if(Object.getPrototypeOf(this)!==Sn)throw new re("Use 'new' to construct "+j);if(Mt.constructor_body===void 0)throw new re(j+" has no accessible constructor");var ti=Mt.constructor_body[arguments.length];if(ti===void 0)throw new re("Tried to invoke ctor of "+j+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(Mt.constructor_body).toString()+") parameters instead!");return ti.apply(this,arguments)}),Sn=Object.create(Fe,{constructor:{value:He}});He.prototype=Sn;var Mt=new Rt(j,He,Sn,V,ve,v,_,S),Wn=new qe(j,Mt,!0,!1,!1),Fn=new qe(j+"*",Mt,!1,!1,!1),ei=new qe(j+" const*",Mt,!1,!0,!1);return jt[e]={pointerType:Fn,constPointerType:ei},ur(te,He),[Wn,Fn,ei]})}function xr(e,t){for(var o=[],c=0;c<e;c++)o.push(he[t+c*4>>2]);return o}function Wr(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var o=Ze(e.name||"unknownFunctionName",function(){});o.prototype=e.prototype;var c=new o,p=e.apply(c,t);return p instanceof Object?p:c}function We(e,t,o,c,p){var v=t.length;v<2&&Q("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var g=t[1]!==null&&o!==null,_=!1,P=1;P<t.length;++P)if(t[P]!==null&&t[P].destructorFunction===void 0){_=!0;break}for(var S=t[0].name!=="void",j="",B="",P=0;P<v-2;++P)j+=(P!==0?", ":"")+"arg"+P,B+=(P!==0?", ":"")+"arg"+P+"Wired";var V="return function "+Ft(e)+"("+j+`) {
if (arguments.length !== `+(v-2)+`) {
throwBindingError('function `+e+" called with ' + arguments.length + ' arguments, expected "+(v-2)+` args!');
}
`;_&&(V+=`var destructors = [];
`);var te=_?"destructors":"null",Z=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],ve=[Q,c,p,ge,t[0],t[1]];g&&(V+="var thisWired = classParam.toWireType("+te+`, this);
`);for(var P=0;P<v-2;++P)V+="var arg"+P+"Wired = argType"+P+".toWireType("+te+", arg"+P+"); // "+t[P+2].name+`
`,Z.push("argType"+P),ve.push(t[P+2]);if(g&&(B="thisWired"+(B.length>0?", ":"")+B),V+=(S?"var rv = ":"")+"invoker(fn"+(B.length>0?", ":"")+B+`);
`,_)V+=`runDestructors(destructors);
`;else for(var P=g?1:2;P<t.length;++P){var Fe=P===1?"thisWired":"arg"+(P-2)+"Wired";t[P].destructorFunction!==null&&(V+=Fe+"_dtor("+Fe+"); // "+t[P].name+`
`,Z.push(Fe+"_dtor"),ve.push(t[P].destructorFunction))}S&&(V+=`var ret = retType.fromWireType(rv);
return ret;
`),V+=`}
`,Z.push(V);var He=Wr(Function,Z).apply(null,ve);return He}function Ge(e,t,o,c,p,v){St(t>0);var g=xr(t,o);p=je(c,p),Ie([],[e],function(_){_=_[0];var P="constructor "+_.name;if(_.registeredClass.constructor_body===void 0&&(_.registeredClass.constructor_body=[]),_.registeredClass.constructor_body[t-1]!==void 0)throw new re("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+_.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return _.registeredClass.constructor_body[t-1]=()=>{cr("Cannot construct "+_.name+" due to unbound types",g)},Ie([],g,function(S){return S.splice(1,0,null),_.registeredClass.constructor_body[t-1]=We(P,S,null,p,v),[]}),[]})}function Qt(e,t,o,c,p,v,g,_){var P=xr(o,c);t=de(t),v=je(p,v),Ie([],[e],function(S){S=S[0];var j=S.name+"."+t;t.startsWith("@@")&&(t=Symbol[t.substring(2)]),_&&S.registeredClass.pureVirtualFunctions.push(t);function B(){cr("Cannot call "+j+" due to unbound types",P)}var V=S.registeredClass.instancePrototype,te=V[t];return te===void 0||te.overloadTable===void 0&&te.className!==S.name&&te.argCount===o-2?(B.argCount=o-2,B.className=S.name,V[t]=B):($t(V,t,j),V[t].overloadTable[o-2]=B),Ie([],P,function(Z){var ve=We(j,Z,S,v,g);return V[t].overloadTable===void 0?(ve.argCount=o-2,V[t]=ve):V[t].overloadTable[o-2]=ve,[]}),[]})}var ke=[],Je=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function dr(e){e>4&&--Je[e].refcount===0&&(Je[e]=void 0,ke.push(e))}function Pt(){for(var e=0,t=5;t<Je.length;++t)Je[t]!==void 0&&++e;return e}function $r(){for(var e=5;e<Je.length;++e)if(Je[e]!==void 0)return Je[e];return null}function ot(){r.count_emval_handles=Pt,r.get_first_emval=$r}var Tt={toValue:e=>(e||Q("Cannot use deleted val. handle = "+e),Je[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:{var t=ke.length?ke.pop():Je.length;return Je[t]={refcount:1,value:e},t}}}};function Zt(e,t){t=de(t),Oe(e,{name:t,fromWireType:function(o){var c=Tt.toValue(o);return dr(o),c},toWireType:function(o,c){return Tt.toHandle(c)},argPackAdvance:8,readValueFromPointer:Ve,destructorFunction:null})}function Ne(e){if(e===null)return"null";var t=typeof e;return t==="object"||t==="array"||t==="function"?e.toString():""+e}function Hr(e,t){switch(t){case 2:return function(o){return this.fromWireType(Vt[o>>2])};case 3:return function(o){return this.fromWireType(Bt[o>>3])};default:throw new TypeError("Unknown float type: "+e)}}function cn(e,t,o){var c=Ce(o);t=de(t),Oe(e,{name:t,fromWireType:function(p){return p},toWireType:function(p,v){return v},argPackAdvance:8,readValueFromPointer:Hr(t,c),destructorFunction:null})}function ln(e,t,o){switch(t){case 0:return o?function(p){return ce[p]}:function(p){return ue[p]};case 1:return o?function(p){return ne[p>>1]}:function(p){return oe[p>>1]};case 2:return o?function(p){return ie[p>>2]}:function(p){return he[p>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function dn(e,t,o,c,p){t=de(t);var v=Ce(o),g=B=>B;if(c===0){var _=32-8*o;g=B=>B<<_>>>_}var P=t.includes("unsigned"),S=(B,V)=>{},j;P?j=function(B,V){return S(V,this.name),V>>>0}:j=function(B,V){return S(V,this.name),V},Oe(e,{name:t,fromWireType:g,toWireType:j,argPackAdvance:8,readValueFromPointer:ln(t,v,c!==0),destructorFunction:null})}function er(e,t,o){var c=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],p=c[t];function v(g){g=g>>2;var _=he,P=_[g],S=_[g+1];return new p(le,S,P)}o=de(o),Oe(e,{name:o,fromWireType:v,argPackAdvance:8,readValueFromPointer:v},{ignoreDuplicateRegistrations:!0})}function Mr(e,t){t=de(t);var o=t==="std::string";Oe(e,{name:t,fromWireType:function(c){var p=he[c>>2],v=c+4,g;if(o)for(var _=v,P=0;P<=p;++P){var S=v+P;if(P==p||ue[S]==0){var j=S-_,B=hr(_,j);g===void 0?g=B:(g+="\0",g+=B),_=S+1}}else{for(var V=new Array(p),P=0;P<p;++P)V[P]=String.fromCharCode(ue[v+P]);g=V.join("")}return x(c),g},toWireType:function(c,p){p instanceof ArrayBuffer&&(p=new Uint8Array(p));var v,g=typeof p=="string";g||p instanceof Uint8Array||p instanceof Uint8ClampedArray||p instanceof Int8Array||Q("Cannot pass non-string to std::string"),o&&g?v=me(p):v=p.length;var _=F(4+v+1),P=_+4;if(he[_>>2]=v,o&&g)Xe(p,P,v+1);else if(g)for(var S=0;S<v;++S){var j=p.charCodeAt(S);j>255&&(x(P),Q("String has UTF-16 code units that do not fit in 8 bits")),ue[P+S]=j}else for(var S=0;S<v;++S)ue[P+S]=p[S];return c!==null&&c.push(x,_),_},argPackAdvance:8,readValueFromPointer:Ve,destructorFunction:function(c){x(c)}})}var pr=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function pn(e,t){for(var o=e,c=o>>1,p=c+t/2;!(c>=p)&&oe[c];)++c;if(o=c<<1,o-e>32&&pr)return pr.decode(ue.subarray(e,o));for(var v="",g=0;!(g>=t/2);++g){var _=ne[e+g*2>>1];if(_==0)break;v+=String.fromCharCode(_)}return v}function vn(e,t,o){if(o===void 0&&(o=2147483647),o<2)return 0;o-=2;for(var c=t,p=o<e.length*2?o/2:e.length,v=0;v<p;++v){var g=e.charCodeAt(v);ne[t>>1]=g,t+=2}return ne[t>>1]=0,t-c}function hn(e){return e.length*2}function gn(e,t){for(var o=0,c="";!(o>=t/4);){var p=ie[e+o*4>>2];if(p==0)break;if(++o,p>=65536){var v=p-65536;c+=String.fromCharCode(55296|v>>10,56320|v&1023)}else c+=String.fromCharCode(p)}return c}function yn(e,t,o){if(o===void 0&&(o=2147483647),o<4)return 0;for(var c=t,p=c+o-4,v=0;v<e.length;++v){var g=e.charCodeAt(v);if(g>=55296&&g<=57343){var _=e.charCodeAt(++v);g=65536+((g&1023)<<10)|_&1023}if(ie[t>>2]=g,t+=4,t+4>p)break}return ie[t>>2]=0,t-c}function _n(e){for(var t=0,o=0;o<e.length;++o){var c=e.charCodeAt(o);c>=55296&&c<=57343&&++o,t+=4}return t}function mn(e,t,o){o=de(o);var c,p,v,g,_;t===2?(c=pn,p=vn,g=hn,v=()=>oe,_=1):t===4&&(c=gn,p=yn,g=_n,v=()=>he,_=2),Oe(e,{name:o,fromWireType:function(P){for(var S=he[P>>2],j=v(),B,V=P+4,te=0;te<=S;++te){var Z=P+4+te*t;if(te==S||j[Z>>_]==0){var ve=Z-V,Fe=c(V,ve);B===void 0?B=Fe:(B+="\0",B+=Fe),V=Z+t}}return x(P),B},toWireType:function(P,S){typeof S!="string"&&Q("Cannot pass non-string to C++ string type "+o);var j=g(S),B=F(4+j+t);return he[B>>2]=j>>_,p(S,B+4,j+t),P!==null&&P.push(x,B),B},argPackAdvance:8,readValueFromPointer:Ve,destructorFunction:function(P){x(P)}})}function Lr(e,t,o,c,p,v){Le[e]={name:de(t),rawConstructor:je(o,c),rawDestructor:je(p,v),fields:[]}}function wn(e,t,o,c,p,v,g,_,P,S){Le[e].fields.push({fieldName:de(t),getterReturnType:o,getter:je(c,p),getterContext:v,setterArgumentType:g,setter:je(_,P),setterContext:S})}function bn(e,t){t=de(t),Oe(e,{isVoid:!0,name:t,argPackAdvance:0,fromWireType:function(){},toWireType:function(o,c){}})}function Vr(){throw 1/0}var Br={};function Pn(e){var t=Br[e];return t===void 0?de(e):t}function Rr(){return typeof globalThis=="object"?globalThis:function(){return Function}()("return this")()}function Nr(e){return e===0?Tt.toHandle(Rr()):(e=Pn(e),Tt.toHandle(Rr()[e]))}function zr(e){e>4&&(Je[e].refcount+=1)}function Er(e,t){var o=Be[e];return o===void 0&&Q(t+" has unknown type "+xe(e)),o}function Tn(e){for(var t="",o=0;o<e;++o)t+=(o!==0?", ":"")+"arg"+o;for(var c=()=>he,p="return function emval_allocator_"+e+`(constructor, argTypes, args) {
  var HEAPU32 = getMemory();
`,o=0;o<e;++o)p+="var argType"+o+" = requireRegisteredType(HEAPU32[((argTypes)>>2)], 'parameter "+o+`');
var arg`+o+" = argType"+o+`.readValueFromPointer(args);
args += argType`+o+`['argPackAdvance'];
argTypes += 4;
`;return p+="var obj = new constructor("+t+`);
return valueToHandle(obj);
}
`,new Function("requireRegisteredType","Module","valueToHandle","getMemory",p)(Er,r,Tt.toHandle,c)}var qr={};function Cn(e,t,o,c){e=Tt.toValue(e);var p=qr[t];return p||(p=Tn(t),qr[t]=p),p(e,o,c)}function Gr(e,t){e=Er(e,"_emval_take_value");var o=e.readValueFromPointer(t);return Tt.toHandle(o)}function An(){ut("")}function $n(e,t,o){ue.copyWithin(e,t,t+o)}function Rn(){return 2147483648}function En(e){try{return at.grow(e-le.byteLength+65535>>>16),Nt(at.buffer),1}catch{}}function Sr(e){var t=ue.length;e=e>>>0;var o=Rn();if(e>o)return!1;let c=(P,S)=>P+(S-P%S)%S;for(var p=1;p<=4;p*=2){var v=t*(1+.2/p);v=Math.min(v,e+100663296);var g=Math.min(o,c(Math.max(e,v),65536)),_=En(g);if(_)return!0}return!1}var kr={};function Et(){return q||"./this.program"}function Ht(){if(!Ht.strings){var e=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:e,_:Et()};for(var o in kr)kr[o]===void 0?delete t[o]:t[o]=kr[o];var c=[];for(var o in t)c.push(o+"="+t[o]);Ht.strings=c}return Ht.strings}function _t(e,t,o){for(var c=0;c<e.length;++c)ce[t++>>0]=e.charCodeAt(c);ce[t>>0]=0}function Jr(e,t){var o=0;return Ht().forEach(function(c,p){var v=t+o;he[e+p*4>>2]=v,_t(c,v),o+=c.length+1}),0}function Xr(e,t){var o=Ht();he[e>>2]=o.length;var c=0;return o.forEach(function(p){c+=p.length+1}),he[t>>2]=c,0}function Dr(e){L(e,new Gt(e))}function Yr(e,t){Dr(e)}var vr=Yr;function Kr(e){return 52}function u(e,t,o,c,p){return 70}var f=[null,[],[]];function h(e,t){var o=f[e];t===0||t===10?((e===1?se:_e)(rr(o,0)),o.length=0):o.push(t)}function b(e,t,o,c){for(var p=0,v=0;v<o;v++){var g=he[t>>2],_=he[t+4>>2];t+=8;for(var P=0;P<_;P++)h(e,ue[g+P]);p+=_}return he[c>>2]=p,0}function C(e){var t=r["_"+e];return t}function k(e,t){ce.set(e,t)}function D(e,t,o,c,p){var v={string:Z=>{var ve=0;if(Z!=null&&Z!==0){var Fe=(Z.length<<2)+1;ve=a(Fe),Xe(Z,ve,Fe)}return ve},array:Z=>{var ve=a(Z.length);return k(Z,ve),ve}};function g(Z){return t==="string"?hr(Z):t==="boolean"?!!Z:Z}var _=C(e),P=[],S=0;if(c)for(var j=0;j<c.length;j++){var B=v[o[j]];B?(S===0&&(S=K()),P[j]=B(c[j])):P[j]=c[j]}var V=_.apply(null,P);function te(Z){return S!==0&&s(S),g(Z)}return V=te(V),V}ft=r.InternalError=et(Error,"InternalError"),ct(),re=r.BindingError=et(Error,"BindingError"),gt(),xt(),it(),Ar=r.UnboundTypeError=et(Error,"UnboundTypeError"),ot();var R={g:Me,A:or,w:ar,F:wr,u:lr,t:Ge,c:Qt,E:Zt,m:cn,b:dn,a:er,l:Mr,h:mn,J:Lr,d:wn,G:bn,x:Vr,i:dr,r:Nr,p:zr,q:Cn,s:Gr,j:An,D:$n,y:Sr,z:Jr,B:Xr,I:vr,C:Kr,v:u,k:b,o:T,n:E,H:O,f:w,e:U};_r(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.L).apply(null,arguments)};var F=r._malloc=function(){return(F=r._malloc=r.asm.N).apply(null,arguments)},x=r._free=function(){return(x=r._free=r.asm.O).apply(null,arguments)},G=r.___getTypeName=function(){return(G=r.___getTypeName=r.asm.P).apply(null,arguments)};r.__embind_initialize_bindings=function(){return(r.__embind_initialize_bindings=r.asm.Q).apply(null,arguments)};var J=r._setThrew=function(){return(J=r._setThrew=r.asm.R).apply(null,arguments)},K=r.stackSave=function(){return(K=r.stackSave=r.asm.S).apply(null,arguments)},s=r.stackRestore=function(){return(s=r.stackRestore=r.asm.T).apply(null,arguments)},a=r.stackAlloc=function(){return(a=r.stackAlloc=r.asm.U).apply(null,arguments)},d=r.___cxa_is_pointer_type=function(){return(d=r.___cxa_is_pointer_type=r.asm.V).apply(null,arguments)};r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.W).apply(null,arguments)};function w(e,t){var o=K();try{Ee(e)(t)}catch(c){if(s(o),c!==c+0)throw c;J(1,0)}}function T(e,t){var o=K();try{return Ee(e)(t)}catch(c){if(s(o),c!==c+0)throw c;J(1,0)}}function U(e,t,o,c){var p=K();try{Ee(e)(t,o,c)}catch(v){if(s(p),v!==v+0)throw v;J(1,0)}}function O(e,t,o,c){var p=K();try{return Ee(e)(t,o,c)}catch(v){if(s(p),v!==v+0)throw v;J(1,0)}}function E(e,t,o){var c=K();try{return Ee(e)(t,o)}catch(p){if(s(c),p!==p+0)throw p;J(1,0)}}r.ccall=D;var n;st=function e(){n||i(),n||(st=e)};function i(e){if(Qe>0||(Zr(),Qe>0))return;function t(){n||(n=!0,r.calledRun=!0,!Lt&&(en(),H(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),tn()))}r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),t()},1)):t()}if(r.preInit)for(typeof r.preInit=="function"&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return i(),A.ready}})();l.exports=m}(Gn)),Gn.exports}var qi=zi(),Gi=zn(qi);const Ji=new URL("/assets/libjpegturbowasm_decode-daqMmuVl.wasm",import.meta.url),Ln={codec:void 0,decoder:void 0};function Xi(){if(Ln.codec)return Promise.resolve();const l=Gi({locateFile:y=>y.endsWith(".wasm")?Ji.toString():y});return new Promise((y,m)=>{l.then($=>{Ln.codec=$,Ln.decoder=new $.JPEGDecoder,y()},m)})}async function Yi(l,y){await Xi();const m=Ln.decoder;m.getEncodedBuffer(l.length).set(l),m.decode();const A=m.getFrameInfo(),r=m.getDecodedBuffer(),H={columns:A.width,rows:A.height,bitsPerPixel:A.bitsPerSample,signed:y.signed,bytesPerPixel:y.bytesPerPixel,componentsPerPixel:A.componentCount},I=Ki(A,r),W={frameInfo:A};return{...y,pixelData:I,imageInfo:H,encodeOptions:W,...W,...H}}function Ki(l,y){return l.isSigned?new Int8Array(y.buffer,y.byteOffset,y.byteLength):new Uint8Array(y.buffer,y.byteOffset,y.byteLength)}const jn={JpegImage:void 0,decodeConfig:{}};function Qi(l){return jn.decodeConfig=l,jn.JpegImage?Promise.resolve():new Promise((y,m)=>{import("./jpeg-DbLkkf_e.js").then($=>{jn.JpegImage=$.default,y()}).catch(m)})}async function Zi(l,y){if(await Qi(),typeof jn.JpegImage>"u")throw new Error("No JPEG Baseline decoder loaded");const m=new jn.JpegImage;if(m.parse(y),m.colorTransform=!1,l.bitsAllocated===8)return l.pixelData=m.getData(l.columns,l.rows),l;if(l.bitsAllocated===16)return l.pixelData=m.getData16(l.columns,l.rows),l}const xn={jpeg:void 0,decodeConfig:{}};function eo(l){return xn.decodeConfig=l,xn.jpeg?Promise.resolve():new Promise((y,m)=>{import("./lossless-BDvWhinS.js").then(({Decoder:$})=>{const A=new $;xn.jpeg=A,y()},m)})}async function oi(l,y){if(await eo(),typeof xn.jpeg>"u")throw new Error("No JPEG Lossless decoder loaded");const m=l.bitsAllocated<=8?1:2,$=y.buffer,A=xn.jpeg.decode($,y.byteOffset,y.length,m);return l.pixelRepresentation===0?l.bitsAllocated===16?(l.pixelData=new Uint16Array(A.buffer),l):(l.pixelData=new Uint8Array(A.buffer),l):(l.pixelData=new Int16Array(A.buffer),l)}var Jn={exports:{}},ai;function to(){return ai||(ai=1,function(l,y){var m=(()=>{var $=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<"u"&&($=$||__filename),function(A){A=A||{};var r=typeof A<"u"?A:{},H,I;r.ready=new Promise(function(u,f){H=u,I=f});var W=Object.assign({},r),q=typeof window=="object",L=typeof importScripts=="function",N=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",M="";function X(u){return r.locateFile?r.locateFile(u,M):M+u}var z,ee,fe;if(N){var Ae=Qr,pe=Qr;L?M=pe.dirname(M)+"/":M=__dirname+"/",z=(u,f)=>(u=ut(u)?new URL(u):pe.normalize(u),Ae.readFileSync(u,f?void 0:"utf8")),fe=u=>{var f=z(u,!0);return f.buffer||(f=new Uint8Array(f)),f},ee=(u,f,h)=>{u=ut(u)?new URL(u):pe.normalize(u),Ae.readFile(u,function(b,C){b?h(b):f(C.buffer)})},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),process.on("uncaughtException",function(u){if(!(u instanceof Fr))throw u}),process.on("unhandledRejection",function(u){throw u}),r.inspect=function(){return"[Emscripten Module object]"}}else(q||L)&&(L?M=self.location.href:typeof document<"u"&&document.currentScript&&(M=document.currentScript.src),$&&(M=$),M.indexOf("blob:")!==0?M=M.substr(0,M.replace(/[?#].*/,"").lastIndexOf("/")+1):M="",z=u=>{var f=new XMLHttpRequest;return f.open("GET",u,!1),f.send(null),f.responseText},L&&(fe=u=>{var f=new XMLHttpRequest;return f.open("GET",u,!1),f.responseType="arraybuffer",f.send(null),new Uint8Array(f.response)}),ee=(u,f,h)=>{var b=new XMLHttpRequest;b.open("GET",u,!0),b.responseType="arraybuffer",b.onload=()=>{if(b.status==200||b.status==0&&b.response){f(b.response);return}h()},b.onerror=h,b.send(null)});r.print||console.log.bind(console);var we=r.printErr||console.warn.bind(console);Object.assign(r,W),W=null,r.arguments&&r.arguments,r.thisProgram&&r.thisProgram,r.quit&&r.quit;var ye;r.wasmBinary&&(ye=r.wasmBinary),r.noExitRuntime,typeof WebAssembly!="object"&&st("no native wasm support detected");var se,_e=!1;function $e(u,f){u||st(f)}var at=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function Lt(u,f,h){for(var b=f+h,C=f;u[C]&&!(C>=b);)++C;if(C-f>16&&u.buffer&&at)return at.decode(u.subarray(f,C));for(var k="";f<C;){var D=u[f++];if(!(D&128)){k+=String.fromCharCode(D);continue}var R=u[f++]&63;if((D&224)==192){k+=String.fromCharCode((D&31)<<6|R);continue}var F=u[f++]&63;if((D&240)==224?D=(D&15)<<12|R<<6|F:D=(D&7)<<18|R<<12|F<<6|u[f++]&63,D<65536)k+=String.fromCharCode(D);else{var x=D-65536;k+=String.fromCharCode(55296|x>>10,56320|x&1023)}}return k}function St(u,f){return u?Lt(me,u,f):""}function tr(u,f,h,b){if(!(b>0))return 0;for(var C=h,k=h+b-1,D=0;D<u.length;++D){var R=u.charCodeAt(D);if(R>=55296&&R<=57343){var F=u.charCodeAt(++D);R=65536+((R&1023)<<10)|F&1023}if(R<=127){if(h>=k)break;f[h++]=R}else if(R<=2047){if(h+1>=k)break;f[h++]=192|R>>6,f[h++]=128|R&63}else if(R<=65535){if(h+2>=k)break;f[h++]=224|R>>12,f[h++]=128|R>>6&63,f[h++]=128|R&63}else{if(h+3>=k)break;f[h++]=240|R>>18,f[h++]=128|R>>12&63,f[h++]=128|R>>6&63,f[h++]=128|R&63}}return f[h]=0,h-C}function rr(u,f,h){return tr(u,me,f,h)}function hr(u){for(var f=0,h=0;h<u.length;++h){var b=u.charCodeAt(h);b<=127?f++:b<=2047?f+=2:b>=55296&&b<=57343?(f+=4,++h):f+=3}return f}var nr,Xe,me,le,ce,ue,ne,oe,ie;function he(u){nr=u,r.HEAP8=Xe=new Int8Array(u),r.HEAP16=le=new Int16Array(u),r.HEAP32=ue=new Int32Array(u),r.HEAPU8=me=new Uint8Array(u),r.HEAPU16=ce=new Uint16Array(u),r.HEAPU32=ne=new Uint32Array(u),r.HEAPF32=oe=new Float32Array(u),r.HEAPF64=ie=new Float64Array(u)}r.INITIAL_MEMORY;var Vt,Bt=[],Nt=[],zt=[];function ir(){if(r.preRun)for(typeof r.preRun=="function"&&(r.preRun=[r.preRun]);r.preRun.length;)Zr(r.preRun.shift());yr(Bt)}function gr(){yr(Nt)}function Or(){if(r.postRun)for(typeof r.postRun=="function"&&(r.postRun=[r.postRun]);r.postRun.length;)tn(r.postRun.shift());yr(zt)}function Zr(u){Bt.unshift(u)}function en(u){Nt.unshift(u)}function tn(u){zt.unshift(u)}var Ct=0,Ye=null;function Ke(u){Ct++,r.monitorRunDependencies&&r.monitorRunDependencies(Ct)}function Qe(u){if(Ct--,r.monitorRunDependencies&&r.monitorRunDependencies(Ct),Ct==0&&Ye){var f=Ye;Ye=null,f()}}function st(u){r.onAbort&&r.onAbort(u),u="Aborted("+u+")",we(u),_e=!0,u+=". Build with -sASSERTIONS for more info.";var f=new WebAssembly.RuntimeError(u);throw I(f),f}var qt="data:application/octet-stream;base64,";function kt(u){return u.startsWith(qt)}function ut(u){return u.startsWith("file://")}var Pe;Pe="charlswasm_decode.wasm",kt(Pe)||(Pe=X(Pe));function be(u){try{if(u==Pe&&ye)return new Uint8Array(ye);if(fe)return fe(u);throw"both async and sync fetching of the wasm failed"}catch(f){st(f)}}function Te(){if(!ye&&(q||L)){if(typeof fetch=="function"&&!ut(Pe))return fetch(Pe,{credentials:"same-origin"}).then(function(u){if(!u.ok)throw"failed to load wasm binary file at '"+Pe+"'";return u.arrayBuffer()}).catch(function(){return be(Pe)});if(ee)return new Promise(function(u,f){ee(Pe,function(h){u(new Uint8Array(h))},f)})}return Promise.resolve().then(function(){return be(Pe)})}function De(){var u={a:kr};function f(D,R){var F=D.exports;r.asm=F,se=r.asm.z,he(se.buffer),Vt=r.asm.C,en(r.asm.A),Qe()}Ke();function h(D){f(D.instance)}function b(D){return Te().then(function(R){return WebAssembly.instantiate(R,u)}).then(function(R){return R}).then(D,function(R){we("failed to asynchronously prepare wasm: "+R),st(R)})}function C(){return!ye&&typeof WebAssembly.instantiateStreaming=="function"&&!kt(Pe)&&!ut(Pe)&&!N&&typeof fetch=="function"?fetch(Pe,{credentials:"same-origin"}).then(function(D){var R=WebAssembly.instantiateStreaming(D,u);return R.then(h,function(F){return we("wasm streaming compile failed: "+F),we("falling back to ArrayBuffer instantiation"),b(h)})}):b(h)}if(r.instantiateWasm)try{var k=r.instantiateWasm(u,f);return k}catch(D){we("Module.instantiateWasm callback failed with error: "+D),I(D)}return C().catch(I),{}}function Fr(u){this.name="ExitStatus",this.message="Program terminated with exit("+u+")",this.status=u}function yr(u){for(;u.length>0;)u.shift()(r)}function _r(u){this.excPtr=u,this.ptr=u-24,this.set_type=function(f){ne[this.ptr+4>>2]=f},this.get_type=function(){return ne[this.ptr+4>>2]},this.set_destructor=function(f){ne[this.ptr+8>>2]=f},this.get_destructor=function(){return ne[this.ptr+8>>2]},this.set_refcount=function(f){ue[this.ptr>>2]=f},this.set_caught=function(f){f=f?1:0,Xe[this.ptr+12>>0]=f},this.get_caught=function(){return Xe[this.ptr+12>>0]!=0},this.set_rethrown=function(f){f=f?1:0,Xe[this.ptr+13>>0]=f},this.get_rethrown=function(){return Xe[this.ptr+13>>0]!=0},this.init=function(f,h){this.set_adjusted_ptr(0),this.set_type(f),this.set_destructor(h),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var f=ue[this.ptr>>2];ue[this.ptr>>2]=f+1},this.release_ref=function(){var f=ue[this.ptr>>2];return ue[this.ptr>>2]=f-1,f===1},this.set_adjusted_ptr=function(f){ne[this.ptr+16>>2]=f},this.get_adjusted_ptr=function(){return ne[this.ptr+16>>2]},this.get_exception_ptr=function(){var f=Yr(this.get_type());if(f)return ne[this.excPtr>>2];var h=this.get_adjusted_ptr();return h!==0?h:this.excPtr}}function Gt(u,f,h){var b=new _r(u);throw b.init(f,h),u}var Dt={};function Ur(u){for(;u.length;){var f=u.pop(),h=u.pop();h(f)}}function Me(u){return this.fromWireType(ue[u>>2])}var Le={},ge={},Ve={},ze=48,Be=57;function Ot(u){if(u===void 0)return"_unknown";u=u.replace(/[^a-zA-Z0-9_]/g,"$");var f=u.charCodeAt(0);return f>=ze&&f<=Be?"_"+u:u}function mr(u,f){return u=Ot(u),new Function("body","return function "+u+`() {
    "use strict";    return body.apply(this, arguments);
};
`)(f)}function Jt(u,f){var h=mr(f,function(b){this.name=f,this.message=b;var C=new Error(b).stack;C!==void 0&&(this.stack=this.toString()+`
`+C.replace(/^Error(:[^\n]*)?\n/,""))});return h.prototype=Object.create(u.prototype),h.prototype.constructor=h,h.prototype.toString=function(){return this.message===void 0?this.name:this.name+": "+this.message},h}var Ft=void 0;function Ze(u){throw new Ft(u)}function et(u,f,h){u.forEach(function(R){Ve[R]=f});function b(R){var F=h(R);F.length!==u.length&&Ze("Mismatched type converter count");for(var x=0;x<u.length;++x)de(u[x],F[x])}var C=new Array(f.length),k=[],D=0;f.forEach((R,F)=>{ge.hasOwnProperty(R)?C[F]=ge[R]:(k.push(R),Le.hasOwnProperty(R)||(Le[R]=[]),Le[R].push(()=>{C[F]=ge[R],++D,D===k.length&&b(C)}))}),k.length===0&&b(C)}function ft(u){var f=Dt[u];delete Dt[u];var h=f.rawConstructor,b=f.rawDestructor,C=f.fields,k=C.map(D=>D.getterReturnType).concat(C.map(D=>D.setterArgumentType));et([u],k,D=>{var R={};return C.forEach((F,x)=>{var G=F.fieldName,J=D[x],K=F.getter,s=F.getterContext,a=D[x+C.length],d=F.setter,w=F.setterContext;R[G]={read:T=>J.fromWireType(K(s,T)),write:(T,U)=>{var O=[];d(w,T,a.toWireType(O,U)),Ur(O)}}}),[{name:f.name,fromWireType:function(F){var x={};for(var G in R)x[G]=R[G].read(F);return b(F),x},toWireType:function(F,x){for(var G in R)if(!(G in x))throw new TypeError('Missing field:  "'+G+'"');var J=h();for(G in R)R[G].write(J,x[G]);return F!==null&&F.push(b,J),J},argPackAdvance:8,readValueFromPointer:Me,destructorFunction:b}]})}function Ue(u,f,h,b,C){}function Ie(u){switch(u){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+u)}}function or(){for(var u=new Array(256),f=0;f<256;++f)u[f]=String.fromCharCode(f);ar=u}var ar=void 0;function Ce(u){for(var f="",h=u;me[h];)f+=ar[me[h++]];return f}var ct=void 0;function Y(u){throw new ct(u)}function de(u,f,h={}){if(!("argPackAdvance"in f))throw new TypeError("registerType registeredInstance requires argPackAdvance");var b=f.name;if(u||Y('type "'+b+'" must have a positive integer typeid pointer'),ge.hasOwnProperty(u)){if(h.ignoreDuplicateRegistrations)return;Y("Cannot register type '"+b+"' twice")}if(ge[u]=f,delete Ve[u],Le.hasOwnProperty(u)){var C=Le[u];delete Le[u],C.forEach(k=>k())}}function re(u,f,h,b,C){var k=Ie(h);f=Ce(f),de(u,{name:f,fromWireType:function(D){return!!D},toWireType:function(D,R){return R?b:C},argPackAdvance:8,readValueFromPointer:function(D){var R;if(h===1)R=Xe;else if(h===2)R=le;else if(h===4)R=ue;else throw new TypeError("Unknown boolean type size: "+f);return this.fromWireType(R[D>>k])},destructorFunction:null})}function Q(u){if(!(this instanceof bt)||!(u instanceof bt))return!1;for(var f=this.$$.ptrType.registeredClass,h=this.$$.ptr,b=u.$$.ptrType.registeredClass,C=u.$$.ptr;f.baseClass;)h=f.upcast(h),f=f.baseClass;for(;b.baseClass;)C=b.upcast(C),b=b.baseClass;return f===b&&h===C}function Oe(u){return{count:u.count,deleteScheduled:u.deleteScheduled,preservePointerOnDelete:u.preservePointerOnDelete,ptr:u.ptr,ptrType:u.ptrType,smartPtr:u.smartPtr,smartPtrType:u.smartPtrType}}function wr(u){function f(h){return h.$$.ptrType.registeredClass.name}Y(f(u)+" instance already deleted")}var br=!1;function Ut(u){}function Xt(u){u.smartPtr?u.smartPtrType.rawDestructor(u.smartPtr):u.ptrType.registeredClass.rawDestructor(u.ptr)}function It(u){u.count.value-=1;var f=u.count.value===0;f&&Xt(u)}function Re(u,f,h){if(f===h)return u;if(h.baseClass===void 0)return null;var b=Re(u,f,h.baseClass);return b===null?null:h.downcast(b)}var mt={};function ae(){return Object.keys(nt).length}function tt(){var u=[];for(var f in nt)nt.hasOwnProperty(f)&&u.push(nt[f]);return u}var jt=[];function Pr(){for(;jt.length;){var u=jt.pop();u.$$.deleteScheduled=!1,u.delete()}}var lt=void 0;function dt(u){lt=u,jt.length&&lt&&lt(Pr)}function pt(){r.getInheritedInstanceCount=ae,r.getLiveInheritedInstances=tt,r.flushPendingDeletes=Pr,r.setDelayFunction=dt}var nt={};function rn(u,f){for(f===void 0&&Y("ptr should not be undefined");u.baseClass;)f=u.upcast(f),u=u.baseClass;return f}function xt(u,f){return f=rn(u,f),nt[f]}function vt(u,f){(!f.ptrType||!f.ptr)&&Ze("makeClassHandle requires ptr and ptrType");var h=!!f.smartPtrType,b=!!f.smartPtr;return h!==b&&Ze("Both smartPtrType and smartPtr must be specified"),f.count={value:1},wt(Object.create(u,{$$:{value:f}}))}function Ir(u){var f=this.getPointee(u);if(!f)return this.destructor(u),null;var h=xt(this.registeredClass,f);if(h!==void 0){if(h.$$.count.value===0)return h.$$.ptr=f,h.$$.smartPtr=u,h.clone();var b=h.clone();return this.destructor(u),b}function C(){return this.isSmartPointer?vt(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:f,smartPtrType:this,smartPtr:u}):vt(this.registeredClass.instancePrototype,{ptrType:this,ptr:u})}var k=this.registeredClass.getActualType(f),D=mt[k];if(!D)return C.call(this);var R;this.isConst?R=D.constPointerType:R=D.pointerType;var F=Re(f,this.registeredClass,R.registeredClass);return F===null?C.call(this):this.isSmartPointer?vt(R.registeredClass.instancePrototype,{ptrType:R,ptr:F,smartPtrType:this,smartPtr:u}):vt(R.registeredClass.instancePrototype,{ptrType:R,ptr:F})}function wt(u){return typeof FinalizationRegistry>"u"?(wt=f=>f,u):(br=new FinalizationRegistry(f=>{It(f.$$)}),wt=f=>{var h=f.$$,b=!!h.smartPtr;if(b){var C={$$:h};br.register(f,C,f)}return f},Ut=f=>br.unregister(f),wt(u))}function sr(){if(this.$$.ptr||wr(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var u=wt(Object.create(Object.getPrototypeOf(this),{$$:{value:Oe(this.$$)}}));return u.$$.count.value+=1,u.$$.deleteScheduled=!1,u}function ht(){this.$$.ptr||wr(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Y("Object already scheduled for deletion"),Ut(this),It(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function At(){return!this.$$.ptr}function Yt(){return this.$$.ptr||wr(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Y("Object already scheduled for deletion"),jt.push(this),jt.length===1&&lt&&lt(Pr),this.$$.deleteScheduled=!0,this}function nn(){bt.prototype.isAliasOf=Q,bt.prototype.clone=sr,bt.prototype.delete=ht,bt.prototype.isDeleted=At,bt.prototype.deleteLater=Yt}function bt(){}function Wt(u,f,h){if(u[f].overloadTable===void 0){var b=u[f];u[f]=function(){return u[f].overloadTable.hasOwnProperty(arguments.length)||Y("Function '"+h+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+u[f].overloadTable+")!"),u[f].overloadTable[arguments.length].apply(this,arguments)},u[f].overloadTable=[],u[f].overloadTable[b.argCount]=b}}function gt(u,f,h){r.hasOwnProperty(u)?((h===void 0||r[u].overloadTable!==void 0&&r[u].overloadTable[h]!==void 0)&&Y("Cannot register public name '"+u+"' twice"),Wt(r,u,u),r.hasOwnProperty(h)&&Y("Cannot register multiple overloads of a function with the same number of arguments ("+h+")!"),r[u].overloadTable[h]=f):(r[u]=f,h!==void 0&&(r[u].numArguments=h))}function yt(u,f,h,b,C,k,D,R){this.name=u,this.constructor=f,this.instancePrototype=h,this.rawDestructor=b,this.baseClass=C,this.getActualType=k,this.upcast=D,this.downcast=R,this.pureVirtualFunctions=[]}function $t(u,f,h){for(;f!==h;)f.upcast||Y("Expected null or instance of "+h.name+", got an instance of "+f.name),u=f.upcast(u),f=f.baseClass;return u}function on(u,f){if(f===null)return this.isReference&&Y("null is not a valid "+this.name),0;f.$$||Y('Cannot pass "'+ot(f)+'" as a '+this.name),f.$$.ptr||Y("Cannot pass deleted object as a pointer of type "+this.name);var h=f.$$.ptrType.registeredClass,b=$t(f.$$.ptr,h,this.registeredClass);return b}function Rt(u,f){var h;if(f===null)return this.isReference&&Y("null is not a valid "+this.name),this.isSmartPointer?(h=this.rawConstructor(),u!==null&&u.push(this.rawDestructor,h),h):0;f.$$||Y('Cannot pass "'+ot(f)+'" as a '+this.name),f.$$.ptr||Y("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&f.$$.ptrType.isConst&&Y("Cannot convert argument of type "+(f.$$.smartPtrType?f.$$.smartPtrType.name:f.$$.ptrType.name)+" to parameter type "+this.name);var b=f.$$.ptrType.registeredClass;if(h=$t(f.$$.ptr,b,this.registeredClass),this.isSmartPointer)switch(f.$$.smartPtr===void 0&&Y("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:f.$$.smartPtrType===this?h=f.$$.smartPtr:Y("Cannot convert argument of type "+(f.$$.smartPtrType?f.$$.smartPtrType.name:f.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:h=f.$$.smartPtr;break;case 2:if(f.$$.smartPtrType===this)h=f.$$.smartPtr;else{var C=f.clone();h=this.rawShare(h,Pt.toHandle(function(){C.delete()})),u!==null&&u.push(this.rawDestructor,h)}break;default:Y("Unsupporting sharing policy")}return h}function Tr(u,f){if(f===null)return this.isReference&&Y("null is not a valid "+this.name),0;f.$$||Y('Cannot pass "'+ot(f)+'" as a '+this.name),f.$$.ptr||Y("Cannot pass deleted object as a pointer of type "+this.name),f.$$.ptrType.isConst&&Y("Cannot convert argument of type "+f.$$.ptrType.name+" to parameter type "+this.name);var h=f.$$.ptrType.registeredClass,b=$t(f.$$.ptr,h,this.registeredClass);return b}function an(u){return this.rawGetPointee&&(u=this.rawGetPointee(u)),u}function sn(u){this.rawDestructor&&this.rawDestructor(u)}function un(u){u!==null&&u.delete()}function fn(){Se.prototype.getPointee=an,Se.prototype.destructor=sn,Se.prototype.argPackAdvance=8,Se.prototype.readValueFromPointer=Me,Se.prototype.deleteObject=un,Se.prototype.fromWireType=Ir}function Se(u,f,h,b,C,k,D,R,F,x,G){this.name=u,this.registeredClass=f,this.isReference=h,this.isConst=b,this.isSmartPointer=C,this.pointeeType=k,this.sharingPolicy=D,this.rawGetPointee=R,this.rawConstructor=F,this.rawShare=x,this.rawDestructor=G,!C&&f.baseClass===void 0?b?(this.toWireType=on,this.destructorFunction=null):(this.toWireType=Tr,this.destructorFunction=null):this.toWireType=Rt}function Cr(u,f,h){r.hasOwnProperty(u)||Ze("Replacing nonexistant public symbol"),r[u].overloadTable!==void 0&&h!==void 0?r[u].overloadTable[h]=f:(r[u]=f,r[u].argCount=h)}function it(u,f,h){var b=r["dynCall_"+u];return h&&h.length?b.apply(null,[f].concat(h)):b.call(null,f)}var qe=[];function ur(u){var f=qe[u];return f||(u>=qe.length&&(qe.length=u+1),qe[u]=f=Vt.get(u)),f}function fr(u,f,h){if(u.includes("j"))return it(u,f,h);var b=ur(f).apply(null,h);return b}function Kt(u,f){var h=[];return function(){return h.length=0,Object.assign(h,arguments),fr(u,f,h)}}function Ee(u,f){u=Ce(u);function h(){return u.includes("j")?Kt(u,f):ur(f)}var b=h();return typeof b!="function"&&Y("unknown function pointer with signature "+u+": "+f),b}var jr=void 0;function rt(u){var f=Ht(u),h=Ce(f);return _t(f),h}function je(u,f){var h=[],b={};function C(k){if(!b[k]&&!ge[k]){if(Ve[k]){Ve[k].forEach(C);return}h.push(k),b[k]=!0}}throw f.forEach(C),new jr(u+": "+h.map(rt).join([", "]))}function Ar(u,f,h,b,C,k,D,R,F,x,G,J,K){G=Ce(G),k=Ee(C,k),R&&(R=Ee(D,R)),x&&(x=Ee(F,x)),K=Ee(J,K);var s=Ot(G);gt(s,function(){je("Cannot construct "+G+" due to unbound types",[b])}),et([u,f,h],b?[b]:[],function(a){a=a[0];var d,w;b?(d=a.registeredClass,w=d.instancePrototype):w=bt.prototype;var T=mr(s,function(){if(Object.getPrototypeOf(this)!==U)throw new ct("Use 'new' to construct "+G);if(O.constructor_body===void 0)throw new ct(G+" has no accessible constructor");var e=O.constructor_body[arguments.length];if(e===void 0)throw new ct("Tried to invoke ctor of "+G+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(O.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)}),U=Object.create(w,{constructor:{value:T}});T.prototype=U;var O=new yt(G,T,U,K,d,k,R,x),E=new Se(G,O,!0,!1,!1),n=new Se(G+"*",O,!1,!1,!1),i=new Se(G+" const*",O,!1,!0,!1);return mt[u]={pointerType:n,constPointerType:i},Cr(s,T),[E,n,i]})}function xe(u,f){for(var h=[],b=0;b<u;b++)h.push(ne[f+b*4>>2]);return h}function cr(u,f){if(!(u instanceof Function))throw new TypeError("new_ called with constructor type "+typeof u+" which is not a function");var h=mr(u.name||"unknownFunctionName",function(){});h.prototype=u.prototype;var b=new h,C=u.apply(b,f);return C instanceof Object?C:b}function lr(u,f,h,b,C){var k=f.length;k<2&&Y("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var D=f[1]!==null&&h!==null,R=!1,F=1;F<f.length;++F)if(f[F]!==null&&f[F].destructorFunction===void 0){R=!0;break}for(var x=f[0].name!=="void",G="",J="",F=0;F<k-2;++F)G+=(F!==0?", ":"")+"arg"+F,J+=(F!==0?", ":"")+"arg"+F+"Wired";var K="return function "+Ot(u)+"("+G+`) {
if (arguments.length !== `+(k-2)+`) {
throwBindingError('function `+u+" called with ' + arguments.length + ' arguments, expected "+(k-2)+` args!');
}
`;R&&(K+=`var destructors = [];
`);var s=R?"destructors":"null",a=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],d=[Y,b,C,Ur,f[0],f[1]];D&&(K+="var thisWired = classParam.toWireType("+s+`, this);
`);for(var F=0;F<k-2;++F)K+="var arg"+F+"Wired = argType"+F+".toWireType("+s+", arg"+F+"); // "+f[F+2].name+`
`,a.push("argType"+F),d.push(f[F+2]);if(D&&(J="thisWired"+(J.length>0?", ":"")+J),K+=(x?"var rv = ":"")+"invoker(fn"+(J.length>0?", ":"")+J+`);
`,R)K+=`runDestructors(destructors);
`;else for(var F=D?1:2;F<f.length;++F){var w=F===1?"thisWired":"arg"+(F-2)+"Wired";f[F].destructorFunction!==null&&(K+=w+"_dtor("+w+"); // "+f[F].name+`
`,a.push(w+"_dtor"),d.push(f[F].destructorFunction))}x&&(K+=`var ret = retType.fromWireType(rv);
return ret;
`),K+=`}
`,a.push(K);var T=cr(Function,a).apply(null,d);return T}function xr(u,f,h,b,C,k){$e(f>0);var D=xe(f,h);C=Ee(b,C),et([],[u],function(R){R=R[0];var F="constructor "+R.name;if(R.registeredClass.constructor_body===void 0&&(R.registeredClass.constructor_body=[]),R.registeredClass.constructor_body[f-1]!==void 0)throw new ct("Cannot register multiple constructors with identical number of parameters ("+(f-1)+") for class '"+R.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return R.registeredClass.constructor_body[f-1]=()=>{je("Cannot construct "+R.name+" due to unbound types",D)},et([],D,function(x){return x.splice(1,0,null),R.registeredClass.constructor_body[f-1]=lr(F,x,null,C,k),[]}),[]})}function Wr(u,f,h,b,C,k,D,R){var F=xe(h,b);f=Ce(f),k=Ee(C,k),et([],[u],function(x){x=x[0];var G=x.name+"."+f;f.startsWith("@@")&&(f=Symbol[f.substring(2)]),R&&x.registeredClass.pureVirtualFunctions.push(f);function J(){je("Cannot call "+G+" due to unbound types",F)}var K=x.registeredClass.instancePrototype,s=K[f];return s===void 0||s.overloadTable===void 0&&s.className!==x.name&&s.argCount===h-2?(J.argCount=h-2,J.className=x.name,K[f]=J):(Wt(K,f,G),K[f].overloadTable[h-2]=J),et([],F,function(a){var d=lr(G,a,x,k,D);return K[f].overloadTable===void 0?(d.argCount=h-2,K[f]=d):K[f].overloadTable[h-2]=d,[]}),[]})}var We=[],Ge=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Qt(u){u>4&&--Ge[u].refcount===0&&(Ge[u]=void 0,We.push(u))}function ke(){for(var u=0,f=5;f<Ge.length;++f)Ge[f]!==void 0&&++u;return u}function Je(){for(var u=5;u<Ge.length;++u)if(Ge[u]!==void 0)return Ge[u];return null}function dr(){r.count_emval_handles=ke,r.get_first_emval=Je}var Pt={toValue:u=>(u||Y("Cannot use deleted val. handle = "+u),Ge[u].value),toHandle:u=>{switch(u){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:{var f=We.length?We.pop():Ge.length;return Ge[f]={refcount:1,value:u},f}}}};function $r(u,f){f=Ce(f),de(u,{name:f,fromWireType:function(h){var b=Pt.toValue(h);return Qt(h),b},toWireType:function(h,b){return Pt.toHandle(b)},argPackAdvance:8,readValueFromPointer:Me,destructorFunction:null})}function ot(u){if(u===null)return"null";var f=typeof u;return f==="object"||f==="array"||f==="function"?u.toString():""+u}function Tt(u,f){switch(f){case 2:return function(h){return this.fromWireType(oe[h>>2])};case 3:return function(h){return this.fromWireType(ie[h>>3])};default:throw new TypeError("Unknown float type: "+u)}}function Zt(u,f,h){var b=Ie(h);f=Ce(f),de(u,{name:f,fromWireType:function(C){return C},toWireType:function(C,k){return k},argPackAdvance:8,readValueFromPointer:Tt(f,b),destructorFunction:null})}function Ne(u,f,h,b,C,k){var D=xe(f,h);u=Ce(u),C=Ee(b,C),gt(u,function(){je("Cannot call "+u+" due to unbound types",D)},f-1),et([],D,function(R){var F=[R[0],null].concat(R.slice(1));return Cr(u,lr(u,F,null,C,k),f-1),[]})}function Hr(u,f,h){switch(f){case 0:return h?function(C){return Xe[C]}:function(C){return me[C]};case 1:return h?function(C){return le[C>>1]}:function(C){return ce[C>>1]};case 2:return h?function(C){return ue[C>>2]}:function(C){return ne[C>>2]};default:throw new TypeError("Unknown integer type: "+u)}}function cn(u,f,h,b,C){f=Ce(f);var k=Ie(h),D=J=>J;if(b===0){var R=32-8*h;D=J=>J<<R>>>R}var F=f.includes("unsigned"),x=(J,K)=>{},G;F?G=function(J,K){return x(K,this.name),K>>>0}:G=function(J,K){return x(K,this.name),K},de(u,{name:f,fromWireType:D,toWireType:G,argPackAdvance:8,readValueFromPointer:Hr(f,k,b!==0),destructorFunction:null})}function ln(u,f,h){var b=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],C=b[f];function k(D){D=D>>2;var R=ne,F=R[D],x=R[D+1];return new C(nr,x,F)}h=Ce(h),de(u,{name:h,fromWireType:k,argPackAdvance:8,readValueFromPointer:k},{ignoreDuplicateRegistrations:!0})}function dn(u,f){f=Ce(f);var h=f==="std::string";de(u,{name:f,fromWireType:function(b){var C=ne[b>>2],k=b+4,D;if(h)for(var R=k,F=0;F<=C;++F){var x=k+F;if(F==C||me[x]==0){var G=x-R,J=St(R,G);D===void 0?D=J:(D+="\0",D+=J),R=x+1}}else{for(var K=new Array(C),F=0;F<C;++F)K[F]=String.fromCharCode(me[k+F]);D=K.join("")}return _t(b),D},toWireType:function(b,C){C instanceof ArrayBuffer&&(C=new Uint8Array(C));var k,D=typeof C=="string";D||C instanceof Uint8Array||C instanceof Uint8ClampedArray||C instanceof Int8Array||Y("Cannot pass non-string to std::string"),h&&D?k=hr(C):k=C.length;var R=Et(4+k+1),F=R+4;if(ne[R>>2]=k,h&&D)rr(C,F,k+1);else if(D)for(var x=0;x<k;++x){var G=C.charCodeAt(x);G>255&&(_t(F),Y("String has UTF-16 code units that do not fit in 8 bits")),me[F+x]=G}else for(var x=0;x<k;++x)me[F+x]=C[x];return b!==null&&b.push(_t,R),R},argPackAdvance:8,readValueFromPointer:Me,destructorFunction:function(b){_t(b)}})}var er=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function Mr(u,f){for(var h=u,b=h>>1,C=b+f/2;!(b>=C)&&ce[b];)++b;if(h=b<<1,h-u>32&&er)return er.decode(me.subarray(u,h));for(var k="",D=0;!(D>=f/2);++D){var R=le[u+D*2>>1];if(R==0)break;k+=String.fromCharCode(R)}return k}function pr(u,f,h){if(h===void 0&&(h=2147483647),h<2)return 0;h-=2;for(var b=f,C=h<u.length*2?h/2:u.length,k=0;k<C;++k){var D=u.charCodeAt(k);le[f>>1]=D,f+=2}return le[f>>1]=0,f-b}function pn(u){return u.length*2}function vn(u,f){for(var h=0,b="";!(h>=f/4);){var C=ue[u+h*4>>2];if(C==0)break;if(++h,C>=65536){var k=C-65536;b+=String.fromCharCode(55296|k>>10,56320|k&1023)}else b+=String.fromCharCode(C)}return b}function hn(u,f,h){if(h===void 0&&(h=2147483647),h<4)return 0;for(var b=f,C=b+h-4,k=0;k<u.length;++k){var D=u.charCodeAt(k);if(D>=55296&&D<=57343){var R=u.charCodeAt(++k);D=65536+((D&1023)<<10)|R&1023}if(ue[f>>2]=D,f+=4,f+4>C)break}return ue[f>>2]=0,f-b}function gn(u){for(var f=0,h=0;h<u.length;++h){var b=u.charCodeAt(h);b>=55296&&b<=57343&&++h,f+=4}return f}function yn(u,f,h){h=Ce(h);var b,C,k,D,R;f===2?(b=Mr,C=pr,D=pn,k=()=>ce,R=1):f===4&&(b=vn,C=hn,D=gn,k=()=>ne,R=2),de(u,{name:h,fromWireType:function(F){for(var x=ne[F>>2],G=k(),J,K=F+4,s=0;s<=x;++s){var a=F+4+s*f;if(s==x||G[a>>R]==0){var d=a-K,w=b(K,d);J===void 0?J=w:(J+="\0",J+=w),K=a+f}}return _t(F),J},toWireType:function(F,x){typeof x!="string"&&Y("Cannot pass non-string to C++ string type "+h);var G=D(x),J=Et(4+G+f);return ne[J>>2]=G>>R,C(x,J+4,G+f),F!==null&&F.push(_t,J),J},argPackAdvance:8,readValueFromPointer:Me,destructorFunction:function(F){_t(F)}})}function _n(u,f,h,b,C,k){Dt[u]={name:Ce(f),rawConstructor:Ee(h,b),rawDestructor:Ee(C,k),fields:[]}}function mn(u,f,h,b,C,k,D,R,F,x){Dt[u].fields.push({fieldName:Ce(f),getterReturnType:h,getter:Ee(b,C),getterContext:k,setterArgumentType:D,setter:Ee(R,F),setterContext:x})}function Lr(u,f){f=Ce(f),de(u,{isVoid:!0,name:f,argPackAdvance:0,fromWireType:function(){},toWireType:function(h,b){}})}var wn={};function bn(u){var f=wn[u];return f===void 0?Ce(u):f}function Vr(){return typeof globalThis=="object"?globalThis:function(){return Function}()("return this")()}function Br(u){return u===0?Pt.toHandle(Vr()):(u=bn(u),Pt.toHandle(Vr()[u]))}function Pn(u){u>4&&(Ge[u].refcount+=1)}function Rr(u,f){var h=ge[u];return h===void 0&&Y(f+" has unknown type "+rt(u)),h}function Nr(u){for(var f="",h=0;h<u;++h)f+=(h!==0?", ":"")+"arg"+h;for(var b=()=>ne,C="return function emval_allocator_"+u+`(constructor, argTypes, args) {
  var HEAPU32 = getMemory();
`,h=0;h<u;++h)C+="var argType"+h+" = requireRegisteredType(HEAPU32[((argTypes)>>2)], 'parameter "+h+`');
var arg`+h+" = argType"+h+`.readValueFromPointer(args);
args += argType`+h+`['argPackAdvance'];
argTypes += 4;
`;return C+="var obj = new constructor("+f+`);
return valueToHandle(obj);
}
`,new Function("requireRegisteredType","Module","valueToHandle","getMemory",C)(Rr,r,Pt.toHandle,b)}var zr={};function Er(u,f,h,b){u=Pt.toValue(u);var C=zr[f];return C||(C=Nr(f),zr[f]=C),C(u,h,b)}function Tn(u,f){u=Rr(u,"_emval_take_value");var h=u.readValueFromPointer(f);return Pt.toHandle(h)}function qr(){st("")}function Cn(u,f,h){me.copyWithin(u,f,f+h)}function Gr(){return 2147483648}function An(u){try{return se.grow(u-nr.byteLength+65535>>>16),he(se.buffer),1}catch{}}function $n(u){var f=me.length;u=u>>>0;var h=Gr();if(u>h)return!1;let b=(F,x)=>F+(x-F%x)%x;for(var C=1;C<=4;C*=2){var k=f*(1+.2/C);k=Math.min(k,u+100663296);var D=Math.min(h,b(Math.max(u,k),65536)),R=An(D);if(R)return!0}return!1}function Rn(u){var f=r["_"+u];return f}function En(u,f){Xe.set(u,f)}function Sr(u,f,h,b,C){var k={string:a=>{var d=0;if(a!=null&&a!==0){var w=(a.length<<2)+1;d=Dr(w),rr(a,d,w)}return d},array:a=>{var d=Dr(a.length);return En(a,d),d}};function D(a){return f==="string"?St(a):f==="boolean"?!!a:a}var R=Rn(u),F=[],x=0;if(b)for(var G=0;G<b.length;G++){var J=k[h[G]];J?(x===0&&(x=Jr()),F[G]=J(b[G])):F[G]=b[G]}var K=R.apply(null,F);function s(a){return x!==0&&Xr(x),D(a)}return K=s(K),K}Ft=r.InternalError=Jt(Error,"InternalError"),or(),ct=r.BindingError=Jt(Error,"BindingError"),nn(),pt(),fn(),jr=r.UnboundTypeError=Jt(Error,"UnboundTypeError"),dr();var kr={h:Gt,q:ft,r:Ue,w:re,p:Ar,o:xr,c:Wr,v:$r,k:Zt,e:Ne,b:cn,a:ln,j:dn,g:yn,u:_n,d:mn,x:Lr,i:Qt,m:Br,l:Pn,y:Er,n:Tn,f:qr,t:Cn,s:$n};De(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.A).apply(null,arguments)};var Et=r._malloc=function(){return(Et=r._malloc=r.asm.B).apply(null,arguments)},Ht=r.___getTypeName=function(){return(Ht=r.___getTypeName=r.asm.D).apply(null,arguments)};r.__embind_initialize_bindings=function(){return(r.__embind_initialize_bindings=r.asm.E).apply(null,arguments)};var _t=r._free=function(){return(_t=r._free=r.asm.F).apply(null,arguments)},Jr=r.stackSave=function(){return(Jr=r.stackSave=r.asm.G).apply(null,arguments)},Xr=r.stackRestore=function(){return(Xr=r.stackRestore=r.asm.H).apply(null,arguments)},Dr=r.stackAlloc=function(){return(Dr=r.stackAlloc=r.asm.I).apply(null,arguments)},Yr=r.___cxa_is_pointer_type=function(){return(Yr=r.___cxa_is_pointer_type=r.asm.J).apply(null,arguments)};r.ccall=Sr;var vr;Ye=function u(){vr||Kr(),vr||(Ye=u)};function Kr(u){if(Ct>0||(ir(),Ct>0))return;function f(){vr||(vr=!0,r.calledRun=!0,!_e&&(gr(),H(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),Or()))}r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),f()},1)):f()}if(r.preInit)for(typeof r.preInit=="function"&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return Kr(),A.ready}})();l.exports=m}(Jn)),Jn.exports}var ro=to(),no=zn(ro);const io=new URL("/assets/charlswasm_decode-484ovEoR.wasm",import.meta.url),On={codec:void 0,decoder:void 0,decodeConfig:{}};function oo(l){return typeof l=="number"?On.codec.getExceptionMessage(l):l}function ao(l){if(On.decodeConfig=l,On.codec)return Promise.resolve();const y=no({locateFile:m=>m.endsWith(".wasm")?io.toString():m});return new Promise((m,$)=>{y.then(A=>{On.codec=A,On.decoder=new A.JpegLSDecoder,m()},$)})}async function si(l,y){try{await ao();const m=On.decoder;m.getEncodedBuffer(l.length).set(l),m.decode();const A=m.getFrameInfo(),r=m.getInterleaveMode(),H=m.getNearLossless(),I=m.getDecodedBuffer(),W={columns:A.width,rows:A.height,bitsPerPixel:A.bitsPerSample,signed:y.signed,bytesPerPixel:y.bytesPerPixel,componentsPerPixel:A.componentCount},q=so(A,I,y.signed),L={nearLossless:H,interleaveMode:r,frameInfo:A};return{...y,pixelData:q,imageInfo:W,encodeOptions:L,...L,...W}}catch(m){throw oo(m)}}function so(l,y,m){return l.bitsPerSample>8?m?new Int16Array(y.buffer,y.byteOffset,y.byteLength/2):new Uint16Array(y.buffer,y.byteOffset,y.byteLength/2):m?new Int8Array(y.buffer,y.byteOffset,y.byteLength):new Uint8Array(y.buffer,y.byteOffset,y.byteLength)}var Xn={exports:{}},ui;function uo(){return ui||(ui=1,function(l,y){var m=(()=>{var $=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<"u"&&($=$||__filename),function(A){A=A||{};var r=typeof A<"u"?A:{},H,I;r.ready=new Promise(function(s,a){H=s,I=a});var W=Object.assign({},r),q="./this.program",L=typeof window=="object",N=typeof importScripts=="function",M=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",X="";function z(s){return r.locateFile?r.locateFile(s,X):X+s}var ee,fe,Ae;if(M){var pe=Qr,we=Qr;N?X=we.dirname(X)+"/":X=__dirname+"/",ee=(s,a)=>(s=be(s)?new URL(s):we.normalize(s),pe.readFileSync(s,a?void 0:"utf8")),Ae=s=>{var a=ee(s,!0);return a.buffer||(a=new Uint8Array(a)),a},fe=(s,a,d)=>{s=be(s)?new URL(s):we.normalize(s),pe.readFile(s,function(w,T){w?d(w):a(T.buffer)})},process.argv.length>1&&(q=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",function(s){if(!(s instanceof _r))throw s}),process.on("unhandledRejection",function(s){throw s}),r.inspect=function(){return"[Emscripten Module object]"}}else(L||N)&&(N?X=self.location.href:typeof document<"u"&&document.currentScript&&(X=document.currentScript.src),$&&(X=$),X.indexOf("blob:")!==0?X=X.substr(0,X.replace(/[?#].*/,"").lastIndexOf("/")+1):X="",ee=s=>{var a=new XMLHttpRequest;return a.open("GET",s,!1),a.send(null),a.responseText},N&&(Ae=s=>{var a=new XMLHttpRequest;return a.open("GET",s,!1),a.responseType="arraybuffer",a.send(null),new Uint8Array(a.response)}),fe=(s,a,d)=>{var w=new XMLHttpRequest;w.open("GET",s,!0),w.responseType="arraybuffer",w.onload=()=>{if(w.status==200||w.status==0&&w.response){a(w.response);return}d()},w.onerror=d,w.send(null)});var ye=r.print||console.log.bind(console),se=r.printErr||console.warn.bind(console);Object.assign(r,W),W=null,r.arguments&&r.arguments,r.thisProgram&&(q=r.thisProgram),r.quit&&r.quit;var _e;r.wasmBinary&&(_e=r.wasmBinary),r.noExitRuntime,typeof WebAssembly!="object"&&kt("no native wasm support detected");var $e,at=!1;function Lt(s,a){s||kt(a)}var St=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function tr(s,a,d){for(var w=a+d,T=a;s[T]&&!(T>=w);)++T;if(T-a>16&&s.buffer&&St)return St.decode(s.subarray(a,T));for(var U="";a<T;){var O=s[a++];if(!(O&128)){U+=String.fromCharCode(O);continue}var E=s[a++]&63;if((O&224)==192){U+=String.fromCharCode((O&31)<<6|E);continue}var n=s[a++]&63;if((O&240)==224?O=(O&15)<<12|E<<6|n:O=(O&7)<<18|E<<12|n<<6|s[a++]&63,O<65536)U+=String.fromCharCode(O);else{var i=O-65536;U+=String.fromCharCode(55296|i>>10,56320|i&1023)}}return U}function rr(s,a){return s?tr(ce,s,a):""}function hr(s,a,d,w){if(!(w>0))return 0;for(var T=d,U=d+w-1,O=0;O<s.length;++O){var E=s.charCodeAt(O);if(E>=55296&&E<=57343){var n=s.charCodeAt(++O);E=65536+((E&1023)<<10)|n&1023}if(E<=127){if(d>=U)break;a[d++]=E}else if(E<=2047){if(d+1>=U)break;a[d++]=192|E>>6,a[d++]=128|E&63}else if(E<=65535){if(d+2>=U)break;a[d++]=224|E>>12,a[d++]=128|E>>6&63,a[d++]=128|E&63}else{if(d+3>=U)break;a[d++]=240|E>>18,a[d++]=128|E>>12&63,a[d++]=128|E>>6&63,a[d++]=128|E&63}}return a[d]=0,d-T}function nr(s,a,d){return hr(s,ce,a,d)}function Xe(s){for(var a=0,d=0;d<s.length;++d){var w=s.charCodeAt(d);w<=127?a++:w<=2047?a+=2:w>=55296&&w<=57343?(a+=4,++d):a+=3}return a}var me,le,ce,ue,ne,oe,ie,he,Vt;function Bt(s){me=s,r.HEAP8=le=new Int8Array(s),r.HEAP16=ue=new Int16Array(s),r.HEAP32=oe=new Int32Array(s),r.HEAPU8=ce=new Uint8Array(s),r.HEAPU16=ne=new Uint16Array(s),r.HEAPU32=ie=new Uint32Array(s),r.HEAPF32=he=new Float32Array(s),r.HEAPF64=Vt=new Float64Array(s)}r.INITIAL_MEMORY;var Nt,zt=[],ir=[],gr=[];function Or(){if(r.preRun)for(typeof r.preRun=="function"&&(r.preRun=[r.preRun]);r.preRun.length;)tn(r.preRun.shift());Gt(zt)}function Zr(){Gt(ir)}function en(){if(r.postRun)for(typeof r.postRun=="function"&&(r.postRun=[r.postRun]);r.postRun.length;)Ye(r.postRun.shift());Gt(gr)}function tn(s){zt.unshift(s)}function Ct(s){ir.unshift(s)}function Ye(s){gr.unshift(s)}var Ke=0,Qe=null;function st(s){Ke++,r.monitorRunDependencies&&r.monitorRunDependencies(Ke)}function qt(s){if(Ke--,r.monitorRunDependencies&&r.monitorRunDependencies(Ke),Ke==0&&Qe){var a=Qe;Qe=null,a()}}function kt(s){r.onAbort&&r.onAbort(s),s="Aborted("+s+")",se(s),at=!0,s+=". Build with -sASSERTIONS for more info.";var a=new WebAssembly.RuntimeError(s);throw I(a),a}var ut="data:application/octet-stream;base64,";function Pe(s){return s.startsWith(ut)}function be(s){return s.startsWith("file://")}var Te;Te="openjpegwasm_decode.wasm",Pe(Te)||(Te=z(Te));function De(s){try{if(s==Te&&_e)return new Uint8Array(_e);if(Ae)return Ae(s);throw"both async and sync fetching of the wasm failed"}catch(a){kt(a)}}function Fr(){if(!_e&&(L||N)){if(typeof fetch=="function"&&!be(Te))return fetch(Te,{credentials:"same-origin"}).then(function(s){if(!s.ok)throw"failed to load wasm binary file at '"+Te+"'";return s.arrayBuffer()}).catch(function(){return De(Te)});if(fe)return new Promise(function(s,a){fe(Te,function(d){s(new Uint8Array(d))},a)})}return Promise.resolve().then(function(){return De(Te)})}function yr(){var s={a:b};function a(O,E){var n=O.exports;r.asm=n,$e=r.asm.E,Bt($e.buffer),Nt=r.asm.G,Ct(r.asm.F),qt()}st();function d(O){a(O.instance)}function w(O){return Fr().then(function(E){return WebAssembly.instantiate(E,s)}).then(function(E){return E}).then(O,function(E){se("failed to asynchronously prepare wasm: "+E),kt(E)})}function T(){return!_e&&typeof WebAssembly.instantiateStreaming=="function"&&!Pe(Te)&&!be(Te)&&!M&&typeof fetch=="function"?fetch(Te,{credentials:"same-origin"}).then(function(O){var E=WebAssembly.instantiateStreaming(O,s);return E.then(d,function(n){return se("wasm streaming compile failed: "+n),se("falling back to ArrayBuffer instantiation"),w(d)})}):w(d)}if(r.instantiateWasm)try{var U=r.instantiateWasm(s,a);return U}catch(O){se("Module.instantiateWasm callback failed with error: "+O),I(O)}return T().catch(I),{}}function _r(s){this.name="ExitStatus",this.message="Program terminated with exit("+s+")",this.status=s}function Gt(s){for(;s.length>0;)s.shift()(r)}function Dt(s){this.excPtr=s,this.ptr=s-24,this.set_type=function(a){ie[this.ptr+4>>2]=a},this.get_type=function(){return ie[this.ptr+4>>2]},this.set_destructor=function(a){ie[this.ptr+8>>2]=a},this.get_destructor=function(){return ie[this.ptr+8>>2]},this.set_refcount=function(a){oe[this.ptr>>2]=a},this.set_caught=function(a){a=a?1:0,le[this.ptr+12>>0]=a},this.get_caught=function(){return le[this.ptr+12>>0]!=0},this.set_rethrown=function(a){a=a?1:0,le[this.ptr+13>>0]=a},this.get_rethrown=function(){return le[this.ptr+13>>0]!=0},this.init=function(a,d){this.set_adjusted_ptr(0),this.set_type(a),this.set_destructor(d),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var a=oe[this.ptr>>2];oe[this.ptr>>2]=a+1},this.release_ref=function(){var a=oe[this.ptr>>2];return oe[this.ptr>>2]=a-1,a===1},this.set_adjusted_ptr=function(a){ie[this.ptr+16>>2]=a},this.get_adjusted_ptr=function(){return ie[this.ptr+16>>2]},this.get_exception_ptr=function(){var a=G(this.get_type());if(a)return ie[this.excPtr>>2];var d=this.get_adjusted_ptr();return d!==0?d:this.excPtr}}function Ur(s,a,d){var w=new Dt(s);throw w.init(a,d),s}var Me={};function Le(s){for(;s.length;){var a=s.pop(),d=s.pop();d(a)}}function ge(s){return this.fromWireType(oe[s>>2])}var Ve={},ze={},Be={},Ot=48,mr=57;function Jt(s){if(s===void 0)return"_unknown";s=s.replace(/[^a-zA-Z0-9_]/g,"$");var a=s.charCodeAt(0);return a>=Ot&&a<=mr?"_"+s:s}function Ft(s,a){return s=Jt(s),new Function("body","return function "+s+`() {
    "use strict";    return body.apply(this, arguments);
};
`)(a)}function Ze(s,a){var d=Ft(a,function(w){this.name=a,this.message=w;var T=new Error(w).stack;T!==void 0&&(this.stack=this.toString()+`
`+T.replace(/^Error(:[^\n]*)?\n/,""))});return d.prototype=Object.create(s.prototype),d.prototype.constructor=d,d.prototype.toString=function(){return this.message===void 0?this.name:this.name+": "+this.message},d}var et=void 0;function ft(s){throw new et(s)}function Ue(s,a,d){s.forEach(function(E){Be[E]=a});function w(E){var n=d(E);n.length!==s.length&&ft("Mismatched type converter count");for(var i=0;i<s.length;++i)Q(s[i],n[i])}var T=new Array(a.length),U=[],O=0;a.forEach((E,n)=>{ze.hasOwnProperty(E)?T[n]=ze[E]:(U.push(E),Ve.hasOwnProperty(E)||(Ve[E]=[]),Ve[E].push(()=>{T[n]=ze[E],++O,O===U.length&&w(T)}))}),U.length===0&&w(T)}function Ie(s){var a=Me[s];delete Me[s];var d=a.rawConstructor,w=a.rawDestructor,T=a.fields,U=T.map(O=>O.getterReturnType).concat(T.map(O=>O.setterArgumentType));Ue([s],U,O=>{var E={};return T.forEach((n,i)=>{var e=n.fieldName,t=O[i],o=n.getter,c=n.getterContext,p=O[i+T.length],v=n.setter,g=n.setterContext;E[e]={read:_=>t.fromWireType(o(c,_)),write:(_,P)=>{var S=[];v(g,_,p.toWireType(S,P)),Le(S)}}}),[{name:a.name,fromWireType:function(n){var i={};for(var e in E)i[e]=E[e].read(n);return w(n),i},toWireType:function(n,i){for(var e in E)if(!(e in i))throw new TypeError('Missing field:  "'+e+'"');var t=d();for(e in E)E[e].write(t,i[e]);return n!==null&&n.push(w,t),t},argPackAdvance:8,readValueFromPointer:ge,destructorFunction:w}]})}function or(s,a,d,w,T){}function ar(s){switch(s){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+s)}}function Ce(){for(var s=new Array(256),a=0;a<256;++a)s[a]=String.fromCharCode(a);ct=s}var ct=void 0;function Y(s){for(var a="",d=s;ce[d];)a+=ct[ce[d++]];return a}var de=void 0;function re(s){throw new de(s)}function Q(s,a,d={}){if(!("argPackAdvance"in a))throw new TypeError("registerType registeredInstance requires argPackAdvance");var w=a.name;if(s||re('type "'+w+'" must have a positive integer typeid pointer'),ze.hasOwnProperty(s)){if(d.ignoreDuplicateRegistrations)return;re("Cannot register type '"+w+"' twice")}if(ze[s]=a,delete Be[s],Ve.hasOwnProperty(s)){var T=Ve[s];delete Ve[s],T.forEach(U=>U())}}function Oe(s,a,d,w,T){var U=ar(d);a=Y(a),Q(s,{name:a,fromWireType:function(O){return!!O},toWireType:function(O,E){return E?w:T},argPackAdvance:8,readValueFromPointer:function(O){var E;if(d===1)E=le;else if(d===2)E=ue;else if(d===4)E=oe;else throw new TypeError("Unknown boolean type size: "+a);return this.fromWireType(E[O>>U])},destructorFunction:null})}function wr(s){if(!(this instanceof gt)||!(s instanceof gt))return!1;for(var a=this.$$.ptrType.registeredClass,d=this.$$.ptr,w=s.$$.ptrType.registeredClass,T=s.$$.ptr;a.baseClass;)d=a.upcast(d),a=a.baseClass;for(;w.baseClass;)T=w.upcast(T),w=w.baseClass;return a===w&&d===T}function br(s){return{count:s.count,deleteScheduled:s.deleteScheduled,preservePointerOnDelete:s.preservePointerOnDelete,ptr:s.ptr,ptrType:s.ptrType,smartPtr:s.smartPtr,smartPtrType:s.smartPtrType}}function Ut(s){function a(d){return d.$$.ptrType.registeredClass.name}re(a(s)+" instance already deleted")}var Xt=!1;function It(s){}function Re(s){s.smartPtr?s.smartPtrType.rawDestructor(s.smartPtr):s.ptrType.registeredClass.rawDestructor(s.ptr)}function mt(s){s.count.value-=1;var a=s.count.value===0;a&&Re(s)}function ae(s,a,d){if(a===d)return s;if(d.baseClass===void 0)return null;var w=ae(s,a,d.baseClass);return w===null?null:d.downcast(w)}var tt={};function jt(){return Object.keys(xt).length}function Pr(){var s=[];for(var a in xt)xt.hasOwnProperty(a)&&s.push(xt[a]);return s}var lt=[];function dt(){for(;lt.length;){var s=lt.pop();s.$$.deleteScheduled=!1,s.delete()}}var pt=void 0;function nt(s){pt=s,lt.length&&pt&&pt(dt)}function rn(){r.getInheritedInstanceCount=jt,r.getLiveInheritedInstances=Pr,r.flushPendingDeletes=dt,r.setDelayFunction=nt}var xt={};function vt(s,a){for(a===void 0&&re("ptr should not be undefined");s.baseClass;)a=s.upcast(a),s=s.baseClass;return a}function Ir(s,a){return a=vt(s,a),xt[a]}function wt(s,a){(!a.ptrType||!a.ptr)&&ft("makeClassHandle requires ptr and ptrType");var d=!!a.smartPtrType,w=!!a.smartPtr;return d!==w&&ft("Both smartPtrType and smartPtr must be specified"),a.count={value:1},ht(Object.create(s,{$$:{value:a}}))}function sr(s){var a=this.getPointee(s);if(!a)return this.destructor(s),null;var d=Ir(this.registeredClass,a);if(d!==void 0){if(d.$$.count.value===0)return d.$$.ptr=a,d.$$.smartPtr=s,d.clone();var w=d.clone();return this.destructor(s),w}function T(){return this.isSmartPointer?wt(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:a,smartPtrType:this,smartPtr:s}):wt(this.registeredClass.instancePrototype,{ptrType:this,ptr:s})}var U=this.registeredClass.getActualType(a),O=tt[U];if(!O)return T.call(this);var E;this.isConst?E=O.constPointerType:E=O.pointerType;var n=ae(a,this.registeredClass,E.registeredClass);return n===null?T.call(this):this.isSmartPointer?wt(E.registeredClass.instancePrototype,{ptrType:E,ptr:n,smartPtrType:this,smartPtr:s}):wt(E.registeredClass.instancePrototype,{ptrType:E,ptr:n})}function ht(s){return typeof FinalizationRegistry>"u"?(ht=a=>a,s):(Xt=new FinalizationRegistry(a=>{mt(a.$$)}),ht=a=>{var d=a.$$,w=!!d.smartPtr;if(w){var T={$$:d};Xt.register(a,T,a)}return a},It=a=>Xt.unregister(a),ht(s))}function At(){if(this.$$.ptr||Ut(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var s=ht(Object.create(Object.getPrototypeOf(this),{$$:{value:br(this.$$)}}));return s.$$.count.value+=1,s.$$.deleteScheduled=!1,s}function Yt(){this.$$.ptr||Ut(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&re("Object already scheduled for deletion"),It(this),mt(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function nn(){return!this.$$.ptr}function bt(){return this.$$.ptr||Ut(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&re("Object already scheduled for deletion"),lt.push(this),lt.length===1&&pt&&pt(dt),this.$$.deleteScheduled=!0,this}function Wt(){gt.prototype.isAliasOf=wr,gt.prototype.clone=At,gt.prototype.delete=Yt,gt.prototype.isDeleted=nn,gt.prototype.deleteLater=bt}function gt(){}function yt(s,a,d){if(s[a].overloadTable===void 0){var w=s[a];s[a]=function(){return s[a].overloadTable.hasOwnProperty(arguments.length)||re("Function '"+d+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+s[a].overloadTable+")!"),s[a].overloadTable[arguments.length].apply(this,arguments)},s[a].overloadTable=[],s[a].overloadTable[w.argCount]=w}}function $t(s,a,d){r.hasOwnProperty(s)?(re("Cannot register public name '"+s+"' twice"),yt(r,s,s),r.hasOwnProperty(d)&&re("Cannot register multiple overloads of a function with the same number of arguments ("+d+")!"),r[s].overloadTable[d]=a):r[s]=a}function on(s,a,d,w,T,U,O,E){this.name=s,this.constructor=a,this.instancePrototype=d,this.rawDestructor=w,this.baseClass=T,this.getActualType=U,this.upcast=O,this.downcast=E,this.pureVirtualFunctions=[]}function Rt(s,a,d){for(;a!==d;)a.upcast||re("Expected null or instance of "+d.name+", got an instance of "+a.name),s=a.upcast(s),a=a.baseClass;return s}function Tr(s,a){if(a===null)return this.isReference&&re("null is not a valid "+this.name),0;a.$$||re('Cannot pass "'+Zt(a)+'" as a '+this.name),a.$$.ptr||re("Cannot pass deleted object as a pointer of type "+this.name);var d=a.$$.ptrType.registeredClass,w=Rt(a.$$.ptr,d,this.registeredClass);return w}function an(s,a){var d;if(a===null)return this.isReference&&re("null is not a valid "+this.name),this.isSmartPointer?(d=this.rawConstructor(),s!==null&&s.push(this.rawDestructor,d),d):0;a.$$||re('Cannot pass "'+Zt(a)+'" as a '+this.name),a.$$.ptr||re("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&a.$$.ptrType.isConst&&re("Cannot convert argument of type "+(a.$$.smartPtrType?a.$$.smartPtrType.name:a.$$.ptrType.name)+" to parameter type "+this.name);var w=a.$$.ptrType.registeredClass;if(d=Rt(a.$$.ptr,w,this.registeredClass),this.isSmartPointer)switch(a.$$.smartPtr===void 0&&re("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:a.$$.smartPtrType===this?d=a.$$.smartPtr:re("Cannot convert argument of type "+(a.$$.smartPtrType?a.$$.smartPtrType.name:a.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:d=a.$$.smartPtr;break;case 2:if(a.$$.smartPtrType===this)d=a.$$.smartPtr;else{var T=a.clone();d=this.rawShare(d,ot.toHandle(function(){T.delete()})),s!==null&&s.push(this.rawDestructor,d)}break;default:re("Unsupporting sharing policy")}return d}function sn(s,a){if(a===null)return this.isReference&&re("null is not a valid "+this.name),0;a.$$||re('Cannot pass "'+Zt(a)+'" as a '+this.name),a.$$.ptr||re("Cannot pass deleted object as a pointer of type "+this.name),a.$$.ptrType.isConst&&re("Cannot convert argument of type "+a.$$.ptrType.name+" to parameter type "+this.name);var d=a.$$.ptrType.registeredClass,w=Rt(a.$$.ptr,d,this.registeredClass);return w}function un(s){return this.rawGetPointee&&(s=this.rawGetPointee(s)),s}function fn(s){this.rawDestructor&&this.rawDestructor(s)}function Se(s){s!==null&&s.delete()}function Cr(){it.prototype.getPointee=un,it.prototype.destructor=fn,it.prototype.argPackAdvance=8,it.prototype.readValueFromPointer=ge,it.prototype.deleteObject=Se,it.prototype.fromWireType=sr}function it(s,a,d,w,T,U,O,E,n,i,e){this.name=s,this.registeredClass=a,this.isReference=d,this.isConst=w,this.isSmartPointer=T,this.pointeeType=U,this.sharingPolicy=O,this.rawGetPointee=E,this.rawConstructor=n,this.rawShare=i,this.rawDestructor=e,!T&&a.baseClass===void 0?w?(this.toWireType=Tr,this.destructorFunction=null):(this.toWireType=sn,this.destructorFunction=null):this.toWireType=an}function qe(s,a,d){r.hasOwnProperty(s)||ft("Replacing nonexistant public symbol"),r[s].overloadTable!==void 0&&d!==void 0||(r[s]=a,r[s].argCount=d)}function ur(s,a,d){var w=r["dynCall_"+s];return d&&d.length?w.apply(null,[a].concat(d)):w.call(null,a)}var fr=[];function Kt(s){var a=fr[s];return a||(s>=fr.length&&(fr.length=s+1),fr[s]=a=Nt.get(s)),a}function Ee(s,a,d){if(s.includes("j"))return ur(s,a,d);var w=Kt(a).apply(null,d);return w}function jr(s,a){var d=[];return function(){return d.length=0,Object.assign(d,arguments),Ee(s,a,d)}}function rt(s,a){s=Y(s);function d(){return s.includes("j")?jr(s,a):Kt(a)}var w=d();return typeof w!="function"&&re("unknown function pointer with signature "+s+": "+a),w}var je=void 0;function Ar(s){var a=D(s),d=Y(a);return k(a),d}function xe(s,a){var d=[],w={};function T(U){if(!w[U]&&!ze[U]){if(Be[U]){Be[U].forEach(T);return}d.push(U),w[U]=!0}}throw a.forEach(T),new je(s+": "+d.map(Ar).join([", "]))}function cr(s,a,d,w,T,U,O,E,n,i,e,t,o){e=Y(e),U=rt(T,U),E&&(E=rt(O,E)),i&&(i=rt(n,i)),o=rt(t,o);var c=Jt(e);$t(c,function(){xe("Cannot construct "+e+" due to unbound types",[w])}),Ue([s,a,d],w?[w]:[],function(p){p=p[0];var v,g;w?(v=p.registeredClass,g=v.instancePrototype):g=gt.prototype;var _=Ft(c,function(){if(Object.getPrototypeOf(this)!==P)throw new de("Use 'new' to construct "+e);if(S.constructor_body===void 0)throw new de(e+" has no accessible constructor");var te=S.constructor_body[arguments.length];if(te===void 0)throw new de("Tried to invoke ctor of "+e+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(S.constructor_body).toString()+") parameters instead!");return te.apply(this,arguments)}),P=Object.create(g,{constructor:{value:_}});_.prototype=P;var S=new on(e,_,P,o,v,U,E,i),j=new it(e,S,!0,!1,!1),B=new it(e+"*",S,!1,!1,!1),V=new it(e+" const*",S,!1,!0,!1);return tt[s]={pointerType:B,constPointerType:V},qe(c,_),[j,B,V]})}function lr(s,a){for(var d=[],w=0;w<s;w++)d.push(ie[a+w*4>>2]);return d}function xr(s,a){if(!(s instanceof Function))throw new TypeError("new_ called with constructor type "+typeof s+" which is not a function");var d=Ft(s.name||"unknownFunctionName",function(){});d.prototype=s.prototype;var w=new d,T=s.apply(w,a);return T instanceof Object?T:w}function Wr(s,a,d,w,T){var U=a.length;U<2&&re("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var O=a[1]!==null&&d!==null,E=!1,n=1;n<a.length;++n)if(a[n]!==null&&a[n].destructorFunction===void 0){E=!0;break}for(var i=a[0].name!=="void",e="",t="",n=0;n<U-2;++n)e+=(n!==0?", ":"")+"arg"+n,t+=(n!==0?", ":"")+"arg"+n+"Wired";var o="return function "+Jt(s)+"("+e+`) {
if (arguments.length !== `+(U-2)+`) {
throwBindingError('function `+s+" called with ' + arguments.length + ' arguments, expected "+(U-2)+` args!');
}
`;E&&(o+=`var destructors = [];
`);var c=E?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[re,w,T,Le,a[0],a[1]];O&&(o+="var thisWired = classParam.toWireType("+c+`, this);
`);for(var n=0;n<U-2;++n)o+="var arg"+n+"Wired = argType"+n+".toWireType("+c+", arg"+n+"); // "+a[n+2].name+`
`,p.push("argType"+n),v.push(a[n+2]);if(O&&(t="thisWired"+(t.length>0?", ":"")+t),o+=(i?"var rv = ":"")+"invoker(fn"+(t.length>0?", ":"")+t+`);
`,E)o+=`runDestructors(destructors);
`;else for(var n=O?1:2;n<a.length;++n){var g=n===1?"thisWired":"arg"+(n-2)+"Wired";a[n].destructorFunction!==null&&(o+=g+"_dtor("+g+"); // "+a[n].name+`
`,p.push(g+"_dtor"),v.push(a[n].destructorFunction))}i&&(o+=`var ret = retType.fromWireType(rv);
return ret;
`),o+=`}
`,p.push(o);var _=xr(Function,p).apply(null,v);return _}function We(s,a,d,w,T,U){Lt(a>0);var O=lr(a,d);T=rt(w,T),Ue([],[s],function(E){E=E[0];var n="constructor "+E.name;if(E.registeredClass.constructor_body===void 0&&(E.registeredClass.constructor_body=[]),E.registeredClass.constructor_body[a-1]!==void 0)throw new de("Cannot register multiple constructors with identical number of parameters ("+(a-1)+") for class '"+E.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return E.registeredClass.constructor_body[a-1]=()=>{xe("Cannot construct "+E.name+" due to unbound types",O)},Ue([],O,function(i){return i.splice(1,0,null),E.registeredClass.constructor_body[a-1]=Wr(n,i,null,T,U),[]}),[]})}function Ge(s,a,d,w,T,U,O,E){var n=lr(d,w);a=Y(a),U=rt(T,U),Ue([],[s],function(i){i=i[0];var e=i.name+"."+a;a.startsWith("@@")&&(a=Symbol[a.substring(2)]),E&&i.registeredClass.pureVirtualFunctions.push(a);function t(){xe("Cannot call "+e+" due to unbound types",n)}var o=i.registeredClass.instancePrototype,c=o[a];return c===void 0||c.overloadTable===void 0&&c.className!==i.name&&c.argCount===d-2?(t.argCount=d-2,t.className=i.name,o[a]=t):(yt(o,a,e),o[a].overloadTable[d-2]=t),Ue([],n,function(p){var v=Wr(e,p,i,U,O);return o[a].overloadTable===void 0?(v.argCount=d-2,o[a]=v):o[a].overloadTable[d-2]=v,[]}),[]})}var Qt=[],ke=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Je(s){s>4&&--ke[s].refcount===0&&(ke[s]=void 0,Qt.push(s))}function dr(){for(var s=0,a=5;a<ke.length;++a)ke[a]!==void 0&&++s;return s}function Pt(){for(var s=5;s<ke.length;++s)if(ke[s]!==void 0)return ke[s];return null}function $r(){r.count_emval_handles=dr,r.get_first_emval=Pt}var ot={toValue:s=>(s||re("Cannot use deleted val. handle = "+s),ke[s].value),toHandle:s=>{switch(s){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:{var a=Qt.length?Qt.pop():ke.length;return ke[a]={refcount:1,value:s},a}}}};function Tt(s,a){a=Y(a),Q(s,{name:a,fromWireType:function(d){var w=ot.toValue(d);return Je(d),w},toWireType:function(d,w){return ot.toHandle(w)},argPackAdvance:8,readValueFromPointer:ge,destructorFunction:null})}function Zt(s){if(s===null)return"null";var a=typeof s;return a==="object"||a==="array"||a==="function"?s.toString():""+s}function Ne(s,a){switch(a){case 2:return function(d){return this.fromWireType(he[d>>2])};case 3:return function(d){return this.fromWireType(Vt[d>>3])};default:throw new TypeError("Unknown float type: "+s)}}function Hr(s,a,d){var w=ar(d);a=Y(a),Q(s,{name:a,fromWireType:function(T){return T},toWireType:function(T,U){return U},argPackAdvance:8,readValueFromPointer:Ne(a,w),destructorFunction:null})}function cn(s,a,d){switch(a){case 0:return d?function(T){return le[T]}:function(T){return ce[T]};case 1:return d?function(T){return ue[T>>1]}:function(T){return ne[T>>1]};case 2:return d?function(T){return oe[T>>2]}:function(T){return ie[T>>2]};default:throw new TypeError("Unknown integer type: "+s)}}function ln(s,a,d,w,T){a=Y(a);var U=ar(d),O=t=>t;if(w===0){var E=32-8*d;O=t=>t<<E>>>E}var n=a.includes("unsigned"),i=(t,o)=>{},e;n?e=function(t,o){return i(o,this.name),o>>>0}:e=function(t,o){return i(o,this.name),o},Q(s,{name:a,fromWireType:O,toWireType:e,argPackAdvance:8,readValueFromPointer:cn(a,U,w!==0),destructorFunction:null})}function dn(s,a,d){var w=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],T=w[a];function U(O){O=O>>2;var E=ie,n=E[O],i=E[O+1];return new T(me,i,n)}d=Y(d),Q(s,{name:d,fromWireType:U,argPackAdvance:8,readValueFromPointer:U},{ignoreDuplicateRegistrations:!0})}function er(s,a){a=Y(a);var d=a==="std::string";Q(s,{name:a,fromWireType:function(w){var T=ie[w>>2],U=w+4,O;if(d)for(var E=U,n=0;n<=T;++n){var i=U+n;if(n==T||ce[i]==0){var e=i-E,t=rr(E,e);O===void 0?O=t:(O+="\0",O+=t),E=i+1}}else{for(var o=new Array(T),n=0;n<T;++n)o[n]=String.fromCharCode(ce[U+n]);O=o.join("")}return k(w),O},toWireType:function(w,T){T instanceof ArrayBuffer&&(T=new Uint8Array(T));var U,O=typeof T=="string";O||T instanceof Uint8Array||T instanceof Uint8ClampedArray||T instanceof Int8Array||re("Cannot pass non-string to std::string"),d&&O?U=Xe(T):U=T.length;var E=C(4+U+1),n=E+4;if(ie[E>>2]=U,d&&O)nr(T,n,U+1);else if(O)for(var i=0;i<U;++i){var e=T.charCodeAt(i);e>255&&(k(n),re("String has UTF-16 code units that do not fit in 8 bits")),ce[n+i]=e}else for(var i=0;i<U;++i)ce[n+i]=T[i];return w!==null&&w.push(k,E),E},argPackAdvance:8,readValueFromPointer:ge,destructorFunction:function(w){k(w)}})}var Mr=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function pr(s,a){for(var d=s,w=d>>1,T=w+a/2;!(w>=T)&&ne[w];)++w;if(d=w<<1,d-s>32&&Mr)return Mr.decode(ce.subarray(s,d));for(var U="",O=0;!(O>=a/2);++O){var E=ue[s+O*2>>1];if(E==0)break;U+=String.fromCharCode(E)}return U}function pn(s,a,d){if(d===void 0&&(d=2147483647),d<2)return 0;d-=2;for(var w=a,T=d<s.length*2?d/2:s.length,U=0;U<T;++U){var O=s.charCodeAt(U);ue[a>>1]=O,a+=2}return ue[a>>1]=0,a-w}function vn(s){return s.length*2}function hn(s,a){for(var d=0,w="";!(d>=a/4);){var T=oe[s+d*4>>2];if(T==0)break;if(++d,T>=65536){var U=T-65536;w+=String.fromCharCode(55296|U>>10,56320|U&1023)}else w+=String.fromCharCode(T)}return w}function gn(s,a,d){if(d===void 0&&(d=2147483647),d<4)return 0;for(var w=a,T=w+d-4,U=0;U<s.length;++U){var O=s.charCodeAt(U);if(O>=55296&&O<=57343){var E=s.charCodeAt(++U);O=65536+((O&1023)<<10)|E&1023}if(oe[a>>2]=O,a+=4,a+4>T)break}return oe[a>>2]=0,a-w}function yn(s){for(var a=0,d=0;d<s.length;++d){var w=s.charCodeAt(d);w>=55296&&w<=57343&&++d,a+=4}return a}function _n(s,a,d){d=Y(d);var w,T,U,O,E;a===2?(w=pr,T=pn,O=vn,U=()=>ne,E=1):a===4&&(w=hn,T=gn,O=yn,U=()=>ie,E=2),Q(s,{name:d,fromWireType:function(n){for(var i=ie[n>>2],e=U(),t,o=n+4,c=0;c<=i;++c){var p=n+4+c*a;if(c==i||e[p>>E]==0){var v=p-o,g=w(o,v);t===void 0?t=g:(t+="\0",t+=g),o=p+a}}return k(n),t},toWireType:function(n,i){typeof i!="string"&&re("Cannot pass non-string to C++ string type "+d);var e=O(i),t=C(4+e+a);return ie[t>>2]=e>>E,T(i,t+4,e+a),n!==null&&n.push(k,t),t},argPackAdvance:8,readValueFromPointer:ge,destructorFunction:function(n){k(n)}})}function mn(s,a,d,w,T,U){Me[s]={name:Y(a),rawConstructor:rt(d,w),rawDestructor:rt(T,U),fields:[]}}function Lr(s,a,d,w,T,U,O,E,n,i){Me[s].fields.push({fieldName:Y(a),getterReturnType:d,getter:rt(w,T),getterContext:U,setterArgumentType:O,setter:rt(E,n),setterContext:i})}function wn(s,a){a=Y(a),Q(s,{isVoid:!0,name:a,argPackAdvance:0,fromWireType:function(){},toWireType:function(d,w){}})}var bn={};function Vr(s){var a=bn[s];return a===void 0?Y(s):a}function Br(){return typeof globalThis=="object"?globalThis:function(){return Function}()("return this")()}function Pn(s){return s===0?ot.toHandle(Br()):(s=Vr(s),ot.toHandle(Br()[s]))}function Rr(s){s>4&&(ke[s].refcount+=1)}function Nr(s,a){var d=ze[s];return d===void 0&&re(a+" has unknown type "+Ar(s)),d}function zr(s){for(var a="",d=0;d<s;++d)a+=(d!==0?", ":"")+"arg"+d;for(var w=()=>ie,T="return function emval_allocator_"+s+`(constructor, argTypes, args) {
  var HEAPU32 = getMemory();
`,d=0;d<s;++d)T+="var argType"+d+" = requireRegisteredType(HEAPU32[((argTypes)>>2)], 'parameter "+d+`');
var arg`+d+" = argType"+d+`.readValueFromPointer(args);
args += argType`+d+`['argPackAdvance'];
argTypes += 4;
`;return T+="var obj = new constructor("+a+`);
return valueToHandle(obj);
}
`,new Function("requireRegisteredType","Module","valueToHandle","getMemory",T)(Nr,r,ot.toHandle,w)}var Er={};function Tn(s,a,d,w){s=ot.toValue(s);var T=Er[a];return T||(T=zr(a),Er[a]=T),T(s,d,w)}function qr(s,a){s=Nr(s,"_emval_take_value");var d=s.readValueFromPointer(a);return ot.toHandle(d)}function Cn(){kt("")}function Gr(){return 2147483648}function An(){return Gr()}function $n(s,a,d){ce.copyWithin(s,a,a+d)}function Rn(s){try{return $e.grow(s-me.byteLength+65535>>>16),Bt($e.buffer),1}catch{}}function En(s){var a=ce.length;s=s>>>0;var d=Gr();if(s>d)return!1;let w=(n,i)=>n+(i-n%i)%i;for(var T=1;T<=4;T*=2){var U=a*(1+.2/T);U=Math.min(U,s+100663296);var O=Math.min(d,w(Math.max(s,U),65536)),E=Rn(O);if(E)return!0}return!1}var Sr={};function kr(){return q||"./this.program"}function Et(){if(!Et.strings){var s=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:s,_:kr()};for(var d in Sr)Sr[d]===void 0?delete a[d]:a[d]=Sr[d];var w=[];for(var d in a)w.push(d+"="+a[d]);Et.strings=w}return Et.strings}function Ht(s,a,d){for(var w=0;w<s.length;++w)le[a++>>0]=s.charCodeAt(w);le[a>>0]=0}function _t(s,a){var d=0;return Et().forEach(function(w,T){var U=a+d;ie[s+T*4>>2]=U,Ht(w,U),d+=w.length+1}),0}function Jr(s,a){var d=Et();ie[s>>2]=d.length;var w=0;return d.forEach(function(T){w+=T.length+1}),ie[a>>2]=w,0}function Xr(s){return 52}function Dr(s,a,d,w,T){return 70}var Yr=[null,[],[]];function vr(s,a){var d=Yr[s];a===0||a===10?((s===1?ye:se)(tr(d,0)),d.length=0):d.push(a)}function Kr(s,a,d,w){for(var T=0,U=0;U<d;U++){var O=ie[a>>2],E=ie[a+4>>2];a+=8;for(var n=0;n<E;n++)vr(s,ce[O+n]);T+=E}return ie[w>>2]=T,0}function u(s){var a=r["_"+s];return a}function f(s,a){le.set(s,a)}function h(s,a,d,w,T){var U={string:p=>{var v=0;if(p!=null&&p!==0){var g=(p.length<<2)+1;v=x(g),nr(p,v,g)}return v},array:p=>{var v=x(p.length);return f(p,v),v}};function O(p){return a==="string"?rr(p):a==="boolean"?!!p:p}var E=u(s),n=[],i=0;if(w)for(var e=0;e<w.length;e++){var t=U[d[e]];t?(i===0&&(i=R()),n[e]=t(w[e])):n[e]=w[e]}var o=E.apply(null,n);function c(p){return i!==0&&F(i),O(p)}return o=c(o),o}et=r.InternalError=Ze(Error,"InternalError"),Ce(),de=r.BindingError=Ze(Error,"BindingError"),Wt(),rn(),Cr(),je=r.UnboundTypeError=Ze(Error,"UnboundTypeError"),$r();var b={D:Ur,e:Ie,t:or,B:Oe,r:cr,q:We,b:Ge,A:Tt,l:Hr,d:ln,a:dn,k:er,f:_n,g:mn,c:Lr,C:wn,h:Je,o:Pn,m:Rr,n:Tn,p:qr,i:Cn,v:An,z:$n,u:En,w:_t,x:Jr,y:Xr,s:Dr,j:Kr};yr(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.F).apply(null,arguments)};var C=r._malloc=function(){return(C=r._malloc=r.asm.H).apply(null,arguments)},k=r._free=function(){return(k=r._free=r.asm.I).apply(null,arguments)},D=r.___getTypeName=function(){return(D=r.___getTypeName=r.asm.J).apply(null,arguments)};r.__embind_initialize_bindings=function(){return(r.__embind_initialize_bindings=r.asm.K).apply(null,arguments)};var R=r.stackSave=function(){return(R=r.stackSave=r.asm.L).apply(null,arguments)},F=r.stackRestore=function(){return(F=r.stackRestore=r.asm.M).apply(null,arguments)},x=r.stackAlloc=function(){return(x=r.stackAlloc=r.asm.N).apply(null,arguments)},G=r.___cxa_is_pointer_type=function(){return(G=r.___cxa_is_pointer_type=r.asm.O).apply(null,arguments)};r.dynCall_iji=function(){return(r.dynCall_iji=r.asm.P).apply(null,arguments)},r.dynCall_jji=function(){return(r.dynCall_jji=r.asm.Q).apply(null,arguments)},r.dynCall_iiji=function(){return(r.dynCall_iiji=r.asm.R).apply(null,arguments)},r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.S).apply(null,arguments)},r.ccall=h;var J;Qe=function s(){J||K(),J||(Qe=s)};function K(s){if(Ke>0||(Or(),Ke>0))return;function a(){J||(J=!0,r.calledRun=!0,!at&&(Zr(),H(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),en()))}r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),a()},1)):a()}if(r.preInit)for(typeof r.preInit=="function"&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return K(),A.ready}})();l.exports=m}(Xn)),Xn.exports}var fo=uo(),co=zn(fo);const lo=new URL("/assets/openjpegwasm_decode-C4nnCcr6.wasm",import.meta.url),Un={codec:void 0,decoder:void 0,decodeConfig:{}};function po(l){if(Un.decodeConfig=l,Un.codec)return Promise.resolve();const y=co({locateFile:m=>m.endsWith(".wasm")?lo.toString():m});return new Promise((m,$)=>{y.then(A=>{Un.codec=A,Un.decoder=new A.J2KDecoder,m()},$)})}async function fi(l,y){await po();const m=Un.decoder,$=m.getEncodedBuffer(l.length);$.set(l),m.decode();const A=m.getFrameInfo(),r=m.getDecodedBuffer();new Uint8Array(r.length).set(r);const I=`x: ${m.getImageOffset().x}, y: ${m.getImageOffset().y}`,W=m.getNumDecompositions(),q=m.getNumLayers(),L=["unknown","LRCP","RLCP","RPCL","PCRL","CPRL"][m.getProgressionOrder()+1],N=m.getIsReversible(),M=`${m.getBlockDimensions().width} x ${m.getBlockDimensions().height}`,X=`${m.getTileSize().width} x ${m.getTileSize().height}`,z=`${m.getTileOffset().x}, ${m.getTileOffset().y}`,ee=m.getColorSpace(),fe=`${r.length.toLocaleString()} bytes`,Ae=`${(r.length/$.length).toFixed(2)}:1`,pe={columns:A.width,rows:A.height,bitsPerPixel:A.bitsPerSample,signed:A.isSigned,bytesPerPixel:y.bytesPerPixel,componentsPerPixel:A.componentCount},we=vo(A,r),ye={imageOffset:I,numDecompositions:W,numLayers:q,progessionOrder:L,reversible:N,blockDimensions:M,tileSize:X,tileOffset:z,colorTransform:ee,decodedSize:fe,compressionRatio:Ae};return{...y,pixelData:we,imageInfo:pe,encodeOptions:ye,...ye,...pe}}function vo(l,y){return l.bitsPerSample>8?l.isSigned?new Int16Array(y.buffer,y.byteOffset,y.byteLength/2):new Uint16Array(y.buffer,y.byteOffset,y.byteLength/2):l.isSigned?new Int8Array(y.buffer,y.byteOffset,y.byteLength):new Uint8Array(y.buffer,y.byteOffset,y.byteLength)}var Yn={exports:{}},ci;function ho(){return ci||(ci=1,function(l,y){var m=(()=>{var $=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<"u"&&($=$||__filename),function(r){r=r||{};var r=typeof r<"u"?r:{},H,I;r.ready=new Promise(function(n,i){H=n,I=i});var W=Object.assign({},r),q=typeof window=="object",L=typeof importScripts=="function",N=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",M="";function X(n){return r.locateFile?r.locateFile(n,M):M+n}var z,ee,fe;if(N){var Ae=Qr,pe=Qr;L?M=pe.dirname(M)+"/":M=__dirname+"/",z=(n,i)=>(n=Pe(n)?new URL(n):pe.normalize(n),Ae.readFileSync(n,i?void 0:"utf8")),fe=n=>{var i=z(n,!0);return i.buffer||(i=new Uint8Array(i)),i},ee=(n,i,e)=>{n=Pe(n)?new URL(n):pe.normalize(n),Ae.readFile(n,function(t,o){t?e(t):i(o.buffer)})},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),process.on("uncaughtException",function(n){if(!(n instanceof yr))throw n}),process.on("unhandledRejection",function(n){throw n}),r.inspect=function(){return"[Emscripten Module object]"}}else(q||L)&&(L?M=self.location.href:typeof document<"u"&&document.currentScript&&(M=document.currentScript.src),$&&(M=$),M.indexOf("blob:")!==0?M=M.substr(0,M.replace(/[?#].*/,"").lastIndexOf("/")+1):M="",z=n=>{var i=new XMLHttpRequest;return i.open("GET",n,!1),i.send(null),i.responseText},L&&(fe=n=>{var i=new XMLHttpRequest;return i.open("GET",n,!1),i.responseType="arraybuffer",i.send(null),new Uint8Array(i.response)}),ee=(n,i,e)=>{var t=new XMLHttpRequest;t.open("GET",n,!0),t.responseType="arraybuffer",t.onload=()=>{if(t.status==200||t.status==0&&t.response){i(t.response);return}e()},t.onerror=e,t.send(null)});var we=r.print||console.log.bind(console),ye=r.printErr||console.warn.bind(console);Object.assign(r,W),W=null,r.arguments&&r.arguments,r.thisProgram&&r.thisProgram,r.quit&&r.quit;var se;r.wasmBinary&&(se=r.wasmBinary),r.noExitRuntime,typeof WebAssembly!="object"&&qt("no native wasm support detected");var _e,$e=!1;function at(n,i){n||qt(i)}var Lt=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function St(n,i,e){for(var t=i+e,o=i;n[o]&&!(o>=t);)++o;if(o-i>16&&n.buffer&&Lt)return Lt.decode(n.subarray(i,o));for(var c="";i<o;){var p=n[i++];if(!(p&128)){c+=String.fromCharCode(p);continue}var v=n[i++]&63;if((p&224)==192){c+=String.fromCharCode((p&31)<<6|v);continue}var g=n[i++]&63;if((p&240)==224?p=(p&15)<<12|v<<6|g:p=(p&7)<<18|v<<12|g<<6|n[i++]&63,p<65536)c+=String.fromCharCode(p);else{var _=p-65536;c+=String.fromCharCode(55296|_>>10,56320|_&1023)}}return c}function tr(n,i){return n?St(le,n,i):""}function rr(n,i,e,t){if(!(t>0))return 0;for(var o=e,c=e+t-1,p=0;p<n.length;++p){var v=n.charCodeAt(p);if(v>=55296&&v<=57343){var g=n.charCodeAt(++p);v=65536+((v&1023)<<10)|g&1023}if(v<=127){if(e>=c)break;i[e++]=v}else if(v<=2047){if(e+1>=c)break;i[e++]=192|v>>6,i[e++]=128|v&63}else if(v<=65535){if(e+2>=c)break;i[e++]=224|v>>12,i[e++]=128|v>>6&63,i[e++]=128|v&63}else{if(e+3>=c)break;i[e++]=240|v>>18,i[e++]=128|v>>12&63,i[e++]=128|v>>6&63,i[e++]=128|v&63}}return i[e]=0,e-o}function hr(n,i,e){return rr(n,le,i,e)}function nr(n){for(var i=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t<=127?i++:t<=2047?i+=2:t>=55296&&t<=57343?(i+=4,++e):i+=3}return i}var Xe,me,le,ce,ue,ne,oe,ie,he;function Vt(n){Xe=n,r.HEAP8=me=new Int8Array(n),r.HEAP16=ce=new Int16Array(n),r.HEAP32=ne=new Int32Array(n),r.HEAPU8=le=new Uint8Array(n),r.HEAPU16=ue=new Uint16Array(n),r.HEAPU32=oe=new Uint32Array(n),r.HEAPF32=ie=new Float32Array(n),r.HEAPF64=he=new Float64Array(n)}r.INITIAL_MEMORY;var Bt,Nt=[],zt=[],ir=[];function gr(){if(r.preRun)for(typeof r.preRun=="function"&&(r.preRun=[r.preRun]);r.preRun.length;)en(r.preRun.shift());_r(Nt)}function Or(){_r(zt)}function Zr(){if(r.postRun)for(typeof r.postRun=="function"&&(r.postRun=[r.postRun]);r.postRun.length;)Ct(r.postRun.shift());_r(ir)}function en(n){Nt.unshift(n)}function tn(n){zt.unshift(n)}function Ct(n){ir.unshift(n)}var Ye=0,Ke=null;function Qe(n){Ye++,r.monitorRunDependencies&&r.monitorRunDependencies(Ye)}function st(n){if(Ye--,r.monitorRunDependencies&&r.monitorRunDependencies(Ye),Ye==0&&Ke){var i=Ke;Ke=null,i()}}function qt(n){r.onAbort&&r.onAbort(n),n="Aborted("+n+")",ye(n),$e=!0,n+=". Build with -sASSERTIONS for more info.";var i=new WebAssembly.RuntimeError(n);throw I(i),i}var kt="data:application/octet-stream;base64,";function ut(n){return n.startsWith(kt)}function Pe(n){return n.startsWith("file://")}var be;be="openjphjs.wasm",ut(be)||(be=X(be));function Te(n){try{if(n==be&&se)return new Uint8Array(se);if(fe)return fe(n);throw"both async and sync fetching of the wasm failed"}catch(i){qt(i)}}function De(){if(!se&&(q||L)){if(typeof fetch=="function"&&!Pe(be))return fetch(be,{credentials:"same-origin"}).then(function(n){if(!n.ok)throw"failed to load wasm binary file at '"+be+"'";return n.arrayBuffer()}).catch(function(){return Te(be)});if(ee)return new Promise(function(n,i){ee(be,function(e){n(new Uint8Array(e))},i)})}return Promise.resolve().then(function(){return Te(be)})}function Fr(){var n={a:Kr};function i(p,v){var g=p.exports;r.asm=g,_e=r.asm.P,Vt(_e.buffer),Bt=r.asm.T,tn(r.asm.Q),st()}Qe();function e(p){i(p.instance)}function t(p){return De().then(function(v){return WebAssembly.instantiate(v,n)}).then(function(v){return v}).then(p,function(v){ye("failed to asynchronously prepare wasm: "+v),qt(v)})}function o(){return!se&&typeof WebAssembly.instantiateStreaming=="function"&&!ut(be)&&!Pe(be)&&!N&&typeof fetch=="function"?fetch(be,{credentials:"same-origin"}).then(function(p){var v=WebAssembly.instantiateStreaming(p,n);return v.then(e,function(g){return ye("wasm streaming compile failed: "+g),ye("falling back to ArrayBuffer instantiation"),t(e)})}):t(e)}if(r.instantiateWasm)try{var c=r.instantiateWasm(n,i);return c}catch(p){ye("Module.instantiateWasm callback failed with error: "+p),I(p)}return o().catch(I),{}}function yr(n){this.name="ExitStatus",this.message="Program terminated with exit("+n+")",this.status=n}function _r(n){for(;n.length>0;)n.shift()(r)}var Gt=[];function Dt(n){n.add_ref()}function Ur(n){var i=new Be(n);return i.get_caught()||i.set_caught(!0),i.set_rethrown(!1),Gt.push(i),Dt(i),i.get_exception_ptr()}var Me=0,Le=[];function ge(n){var i=Le[n];return i||(n>=Le.length&&(Le.length=n+1),Le[n]=i=Bt.get(n)),i}function Ve(n){if(n.release_ref()&&!n.get_rethrown()){var i=n.get_destructor();i&&ge(i)(n.excPtr),f(n.excPtr)}}function ze(){_setThrew(0);var n=Gt.pop();Ve(n),Me=0}function Be(n){this.excPtr=n,this.ptr=n-24,this.set_type=function(i){oe[this.ptr+4>>2]=i},this.get_type=function(){return oe[this.ptr+4>>2]},this.set_destructor=function(i){oe[this.ptr+8>>2]=i},this.get_destructor=function(){return oe[this.ptr+8>>2]},this.set_refcount=function(i){ne[this.ptr>>2]=i},this.set_caught=function(i){i=i?1:0,me[this.ptr+12>>0]=i},this.get_caught=function(){return me[this.ptr+12>>0]!=0},this.set_rethrown=function(i){i=i?1:0,me[this.ptr+13>>0]=i},this.get_rethrown=function(){return me[this.ptr+13>>0]!=0},this.init=function(i,e){this.set_adjusted_ptr(0),this.set_type(i),this.set_destructor(e),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var i=ne[this.ptr>>2];ne[this.ptr>>2]=i+1},this.release_ref=function(){var i=ne[this.ptr>>2];return ne[this.ptr>>2]=i-1,i===1},this.set_adjusted_ptr=function(i){oe[this.ptr+16>>2]=i},this.get_adjusted_ptr=function(){return oe[this.ptr+16>>2]},this.get_exception_ptr=function(){var i=x(this.get_type());if(i)return oe[this.excPtr>>2];var e=this.get_adjusted_ptr();return e!==0?e:this.excPtr}}function Ot(n){throw Me||(Me=n),n}function mr(){var n=Me;if(!n)return C(0),0;var i=new Be(n);i.set_adjusted_ptr(n);var e=i.get_type();if(!e)return C(0),n;for(var t=0;t<arguments.length;t++){var o=arguments[t];if(o===0||o===e)break;var c=i.ptr+16;if(F(o,e,c))return C(o),n}return C(e),n}function Jt(){var n=Me;if(!n)return C(0),0;var i=new Be(n);i.set_adjusted_ptr(n);var e=i.get_type();if(!e)return C(0),n;for(var t=0;t<arguments.length;t++){var o=arguments[t];if(o===0||o===e)break;var c=i.ptr+16;if(F(o,e,c))return C(o),n}return C(e),n}function Ft(n,i,e){var t=new Be(n);throw t.init(i,e),Me=n,n}var Ze={};function et(n){for(;n.length;){var i=n.pop(),e=n.pop();e(i)}}function ft(n){return this.fromWireType(ne[n>>2])}var Ue={},Ie={},or={},ar=48,Ce=57;function ct(n){if(n===void 0)return"_unknown";n=n.replace(/[^a-zA-Z0-9_]/g,"$");var i=n.charCodeAt(0);return i>=ar&&i<=Ce?"_"+n:n}function Y(n,i){return n=ct(n),new Function("body","return function "+n+`() {
    "use strict";    return body.apply(this, arguments);
};
`)(i)}function de(n,i){var e=Y(i,function(t){this.name=i,this.message=t;var o=new Error(t).stack;o!==void 0&&(this.stack=this.toString()+`
`+o.replace(/^Error(:[^\n]*)?\n/,""))});return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return this.message===void 0?this.name:this.name+": "+this.message},e}var re=void 0;function Q(n){throw new re(n)}function Oe(n,i,e){n.forEach(function(v){or[v]=i});function t(v){var g=e(v);g.length!==n.length&&Q("Mismatched type converter count");for(var _=0;_<n.length;++_)tt(n[_],g[_])}var o=new Array(i.length),c=[],p=0;i.forEach((v,g)=>{Ie.hasOwnProperty(v)?o[g]=Ie[v]:(c.push(v),Ue.hasOwnProperty(v)||(Ue[v]=[]),Ue[v].push(()=>{o[g]=Ie[v],++p,p===c.length&&t(o)}))}),c.length===0&&t(o)}function wr(n){var i=Ze[n];delete Ze[n];var e=i.rawConstructor,t=i.rawDestructor,o=i.fields,c=o.map(p=>p.getterReturnType).concat(o.map(p=>p.setterArgumentType));Oe([n],c,p=>{var v={};return o.forEach((g,_)=>{var P=g.fieldName,S=p[_],j=g.getter,B=g.getterContext,V=p[_+o.length],te=g.setter,Z=g.setterContext;v[P]={read:ve=>S.fromWireType(j(B,ve)),write:(ve,Fe)=>{var He=[];te(Z,ve,V.toWireType(He,Fe)),et(He)}}}),[{name:i.name,fromWireType:function(g){var _={};for(var P in v)_[P]=v[P].read(g);return t(g),_},toWireType:function(g,_){for(var P in v)if(!(P in _))throw new TypeError('Missing field:  "'+P+'"');var S=e();for(P in v)v[P].write(S,_[P]);return g!==null&&g.push(t,S),S},argPackAdvance:8,readValueFromPointer:ft,destructorFunction:t}]})}function br(n,i,e,t,o){}function Ut(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}function Xt(){for(var n=new Array(256),i=0;i<256;++i)n[i]=String.fromCharCode(i);It=n}var It=void 0;function Re(n){for(var i="",e=n;le[e];)i+=It[le[e++]];return i}var mt=void 0;function ae(n){throw new mt(n)}function tt(n,i,e={}){if(!("argPackAdvance"in i))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=i.name;if(n||ae('type "'+t+'" must have a positive integer typeid pointer'),Ie.hasOwnProperty(n)){if(e.ignoreDuplicateRegistrations)return;ae("Cannot register type '"+t+"' twice")}if(Ie[n]=i,delete or[n],Ue.hasOwnProperty(n)){var o=Ue[n];delete Ue[n],o.forEach(c=>c())}}function jt(n,i,e,t,o){var c=Ut(e);i=Re(i),tt(n,{name:i,fromWireType:function(p){return!!p},toWireType:function(p,v){return v?t:o},argPackAdvance:8,readValueFromPointer:function(p){var v;if(e===1)v=me;else if(e===2)v=ce;else if(e===4)v=ne;else throw new TypeError("Unknown boolean type size: "+i);return this.fromWireType(v[p>>c])},destructorFunction:null})}function Pr(n){if(!(this instanceof Se)||!(n instanceof Se))return!1;for(var i=this.$$.ptrType.registeredClass,e=this.$$.ptr,t=n.$$.ptrType.registeredClass,o=n.$$.ptr;i.baseClass;)e=i.upcast(e),i=i.baseClass;for(;t.baseClass;)o=t.upcast(o),t=t.baseClass;return i===t&&e===o}function lt(n){return{count:n.count,deleteScheduled:n.deleteScheduled,preservePointerOnDelete:n.preservePointerOnDelete,ptr:n.ptr,ptrType:n.ptrType,smartPtr:n.smartPtr,smartPtrType:n.smartPtrType}}function dt(n){function i(e){return e.$$.ptrType.registeredClass.name}ae(i(n)+" instance already deleted")}var pt=!1;function nt(n){}function rn(n){n.smartPtr?n.smartPtrType.rawDestructor(n.smartPtr):n.ptrType.registeredClass.rawDestructor(n.ptr)}function xt(n){n.count.value-=1;var i=n.count.value===0;i&&rn(n)}function vt(n,i,e){if(i===e)return n;if(e.baseClass===void 0)return null;var t=vt(n,i,e.baseClass);return t===null?null:e.downcast(t)}var Ir={};function wt(){return Object.keys(Wt).length}function sr(){var n=[];for(var i in Wt)Wt.hasOwnProperty(i)&&n.push(Wt[i]);return n}var ht=[];function At(){for(;ht.length;){var n=ht.pop();n.$$.deleteScheduled=!1,n.delete()}}var Yt=void 0;function nn(n){Yt=n,ht.length&&Yt&&Yt(At)}function bt(){r.getInheritedInstanceCount=wt,r.getLiveInheritedInstances=sr,r.flushPendingDeletes=At,r.setDelayFunction=nn}var Wt={};function gt(n,i){for(i===void 0&&ae("ptr should not be undefined");n.baseClass;)i=n.upcast(i),n=n.baseClass;return i}function yt(n,i){return i=gt(n,i),Wt[i]}function $t(n,i){(!i.ptrType||!i.ptr)&&Q("makeClassHandle requires ptr and ptrType");var e=!!i.smartPtrType,t=!!i.smartPtr;return e!==t&&Q("Both smartPtrType and smartPtr must be specified"),i.count={value:1},Rt(Object.create(n,{$$:{value:i}}))}function on(n){var i=this.getPointee(n);if(!i)return this.destructor(n),null;var e=yt(this.registeredClass,i);if(e!==void 0){if(e.$$.count.value===0)return e.$$.ptr=i,e.$$.smartPtr=n,e.clone();var t=e.clone();return this.destructor(n),t}function o(){return this.isSmartPointer?$t(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:i,smartPtrType:this,smartPtr:n}):$t(this.registeredClass.instancePrototype,{ptrType:this,ptr:n})}var c=this.registeredClass.getActualType(i),p=Ir[c];if(!p)return o.call(this);var v;this.isConst?v=p.constPointerType:v=p.pointerType;var g=vt(i,this.registeredClass,v.registeredClass);return g===null?o.call(this):this.isSmartPointer?$t(v.registeredClass.instancePrototype,{ptrType:v,ptr:g,smartPtrType:this,smartPtr:n}):$t(v.registeredClass.instancePrototype,{ptrType:v,ptr:g})}function Rt(n){return typeof FinalizationRegistry>"u"?(Rt=i=>i,n):(pt=new FinalizationRegistry(i=>{xt(i.$$)}),Rt=i=>{var e=i.$$,t=!!e.smartPtr;if(t){var o={$$:e};pt.register(i,o,i)}return i},nt=i=>pt.unregister(i),Rt(n))}function Tr(){if(this.$$.ptr||dt(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var n=Rt(Object.create(Object.getPrototypeOf(this),{$$:{value:lt(this.$$)}}));return n.$$.count.value+=1,n.$$.deleteScheduled=!1,n}function an(){this.$$.ptr||dt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ae("Object already scheduled for deletion"),nt(this),xt(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function sn(){return!this.$$.ptr}function un(){return this.$$.ptr||dt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ae("Object already scheduled for deletion"),ht.push(this),ht.length===1&&Yt&&Yt(At),this.$$.deleteScheduled=!0,this}function fn(){Se.prototype.isAliasOf=Pr,Se.prototype.clone=Tr,Se.prototype.delete=an,Se.prototype.isDeleted=sn,Se.prototype.deleteLater=un}function Se(){}function Cr(n,i,e){if(n[i].overloadTable===void 0){var t=n[i];n[i]=function(){return n[i].overloadTable.hasOwnProperty(arguments.length)||ae("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[i].overloadTable+")!"),n[i].overloadTable[arguments.length].apply(this,arguments)},n[i].overloadTable=[],n[i].overloadTable[t.argCount]=t}}function it(n,i,e){r.hasOwnProperty(n)?((e===void 0||r[n].overloadTable!==void 0&&r[n].overloadTable[e]!==void 0)&&ae("Cannot register public name '"+n+"' twice"),Cr(r,n,n),r.hasOwnProperty(e)&&ae("Cannot register multiple overloads of a function with the same number of arguments ("+e+")!"),r[n].overloadTable[e]=i):(r[n]=i,e!==void 0&&(r[n].numArguments=e))}function qe(n,i,e,t,o,c,p,v){this.name=n,this.constructor=i,this.instancePrototype=e,this.rawDestructor=t,this.baseClass=o,this.getActualType=c,this.upcast=p,this.downcast=v,this.pureVirtualFunctions=[]}function ur(n,i,e){for(;i!==e;)i.upcast||ae("Expected null or instance of "+e.name+", got an instance of "+i.name),n=i.upcast(n),i=i.baseClass;return n}function fr(n,i){if(i===null)return this.isReference&&ae("null is not a valid "+this.name),0;i.$$||ae('Cannot pass "'+pr(i)+'" as a '+this.name),i.$$.ptr||ae("Cannot pass deleted object as a pointer of type "+this.name);var e=i.$$.ptrType.registeredClass,t=ur(i.$$.ptr,e,this.registeredClass);return t}function Kt(n,i){var e;if(i===null)return this.isReference&&ae("null is not a valid "+this.name),this.isSmartPointer?(e=this.rawConstructor(),n!==null&&n.push(this.rawDestructor,e),e):0;i.$$||ae('Cannot pass "'+pr(i)+'" as a '+this.name),i.$$.ptr||ae("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&i.$$.ptrType.isConst&&ae("Cannot convert argument of type "+(i.$$.smartPtrType?i.$$.smartPtrType.name:i.$$.ptrType.name)+" to parameter type "+this.name);var t=i.$$.ptrType.registeredClass;if(e=ur(i.$$.ptr,t,this.registeredClass),this.isSmartPointer)switch(i.$$.smartPtr===void 0&&ae("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:i.$$.smartPtrType===this?e=i.$$.smartPtr:ae("Cannot convert argument of type "+(i.$$.smartPtrType?i.$$.smartPtrType.name:i.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:e=i.$$.smartPtr;break;case 2:if(i.$$.smartPtrType===this)e=i.$$.smartPtr;else{var o=i.clone();e=this.rawShare(e,er.toHandle(function(){o.delete()})),n!==null&&n.push(this.rawDestructor,e)}break;default:ae("Unsupporting sharing policy")}return e}function Ee(n,i){if(i===null)return this.isReference&&ae("null is not a valid "+this.name),0;i.$$||ae('Cannot pass "'+pr(i)+'" as a '+this.name),i.$$.ptr||ae("Cannot pass deleted object as a pointer of type "+this.name),i.$$.ptrType.isConst&&ae("Cannot convert argument of type "+i.$$.ptrType.name+" to parameter type "+this.name);var e=i.$$.ptrType.registeredClass,t=ur(i.$$.ptr,e,this.registeredClass);return t}function jr(n){return this.rawGetPointee&&(n=this.rawGetPointee(n)),n}function rt(n){this.rawDestructor&&this.rawDestructor(n)}function je(n){n!==null&&n.delete()}function Ar(){xe.prototype.getPointee=jr,xe.prototype.destructor=rt,xe.prototype.argPackAdvance=8,xe.prototype.readValueFromPointer=ft,xe.prototype.deleteObject=je,xe.prototype.fromWireType=on}function xe(n,i,e,t,o,c,p,v,g,_,P){this.name=n,this.registeredClass=i,this.isReference=e,this.isConst=t,this.isSmartPointer=o,this.pointeeType=c,this.sharingPolicy=p,this.rawGetPointee=v,this.rawConstructor=g,this.rawShare=_,this.rawDestructor=P,!o&&i.baseClass===void 0?t?(this.toWireType=fr,this.destructorFunction=null):(this.toWireType=Ee,this.destructorFunction=null):this.toWireType=Kt}function cr(n,i,e){r.hasOwnProperty(n)||Q("Replacing nonexistant public symbol"),r[n].overloadTable!==void 0&&e!==void 0?r[n].overloadTable[e]=i:(r[n]=i,r[n].argCount=e)}function lr(n,i,e){var t=r["dynCall_"+n];return e&&e.length?t.apply(null,[i].concat(e)):t.call(null,i)}function xr(n,i,e){if(n.includes("j"))return lr(n,i,e);var t=ge(i).apply(null,e);return t}function Wr(n,i){var e=[];return function(){return e.length=0,Object.assign(e,arguments),xr(n,i,e)}}function We(n,i){n=Re(n);function e(){return n.includes("j")?Wr(n,i):ge(i)}var t=e();return typeof t!="function"&&ae("unknown function pointer with signature "+n+": "+i),t}var Ge=void 0;function Qt(n){var i=b(n),e=Re(i);return h(i),e}function ke(n,i){var e=[],t={};function o(c){if(!t[c]&&!Ie[c]){if(or[c]){or[c].forEach(o);return}e.push(c),t[c]=!0}}throw i.forEach(o),new Ge(n+": "+e.map(Qt).join([", "]))}function Je(n,i,e,t,o,c,p,v,g,_,P,S,j){P=Re(P),c=We(o,c),v&&(v=We(p,v)),_&&(_=We(g,_)),j=We(S,j);var B=ct(P);it(B,function(){ke("Cannot construct "+P+" due to unbound types",[t])}),Oe([n,i,e],t?[t]:[],function(V){V=V[0];var te,Z;t?(te=V.registeredClass,Z=te.instancePrototype):Z=Se.prototype;var ve=Y(B,function(){if(Object.getPrototypeOf(this)!==Fe)throw new mt("Use 'new' to construct "+P);if(He.constructor_body===void 0)throw new mt(P+" has no accessible constructor");var Fn=He.constructor_body[arguments.length];if(Fn===void 0)throw new mt("Tried to invoke ctor of "+P+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(He.constructor_body).toString()+") parameters instead!");return Fn.apply(this,arguments)}),Fe=Object.create(Z,{constructor:{value:ve}});ve.prototype=Fe;var He=new qe(P,ve,Fe,j,te,c,v,_),Sn=new xe(P,He,!0,!1,!1),Mt=new xe(P+"*",He,!1,!1,!1),Wn=new xe(P+" const*",He,!1,!0,!1);return Ir[n]={pointerType:Mt,constPointerType:Wn},cr(B,ve),[Sn,Mt,Wn]})}function dr(n,i){for(var e=[],t=0;t<n;t++)e.push(oe[i+t*4>>2]);return e}function Pt(n,i){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var e=Y(n.name||"unknownFunctionName",function(){});e.prototype=n.prototype;var t=new e,o=n.apply(t,i);return o instanceof Object?o:t}function $r(n,i,e,t,o){var c=i.length;c<2&&ae("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var p=i[1]!==null&&e!==null,v=!1,g=1;g<i.length;++g)if(i[g]!==null&&i[g].destructorFunction===void 0){v=!0;break}for(var _=i[0].name!=="void",P="",S="",g=0;g<c-2;++g)P+=(g!==0?", ":"")+"arg"+g,S+=(g!==0?", ":"")+"arg"+g+"Wired";var j="return function "+ct(n)+"("+P+`) {
if (arguments.length !== `+(c-2)+`) {
throwBindingError('function `+n+" called with ' + arguments.length + ' arguments, expected "+(c-2)+` args!');
}
`;v&&(j+=`var destructors = [];
`);var B=v?"destructors":"null",V=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],te=[ae,t,o,et,i[0],i[1]];p&&(j+="var thisWired = classParam.toWireType("+B+`, this);
`);for(var g=0;g<c-2;++g)j+="var arg"+g+"Wired = argType"+g+".toWireType("+B+", arg"+g+"); // "+i[g+2].name+`
`,V.push("argType"+g),te.push(i[g+2]);if(p&&(S="thisWired"+(S.length>0?", ":"")+S),j+=(_?"var rv = ":"")+"invoker(fn"+(S.length>0?", ":"")+S+`);
`,v)j+=`runDestructors(destructors);
`;else for(var g=p?1:2;g<i.length;++g){var Z=g===1?"thisWired":"arg"+(g-2)+"Wired";i[g].destructorFunction!==null&&(j+=Z+"_dtor("+Z+"); // "+i[g].name+`
`,V.push(Z+"_dtor"),te.push(i[g].destructorFunction))}_&&(j+=`var ret = retType.fromWireType(rv);
return ret;
`),j+=`}
`,V.push(j);var ve=Pt(Function,V).apply(null,te);return ve}function ot(n,i,e,t,o,c){at(i>0);var p=dr(i,e);o=We(t,o),Oe([],[n],function(v){v=v[0];var g="constructor "+v.name;if(v.registeredClass.constructor_body===void 0&&(v.registeredClass.constructor_body=[]),v.registeredClass.constructor_body[i-1]!==void 0)throw new mt("Cannot register multiple constructors with identical number of parameters ("+(i-1)+") for class '"+v.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return v.registeredClass.constructor_body[i-1]=()=>{ke("Cannot construct "+v.name+" due to unbound types",p)},Oe([],p,function(_){return _.splice(1,0,null),v.registeredClass.constructor_body[i-1]=$r(g,_,null,o,c),[]}),[]})}function Tt(n,i,e,t,o,c,p,v){var g=dr(e,t);i=Re(i),c=We(o,c),Oe([],[n],function(_){_=_[0];var P=_.name+"."+i;i.startsWith("@@")&&(i=Symbol[i.substring(2)]),v&&_.registeredClass.pureVirtualFunctions.push(i);function S(){ke("Cannot call "+P+" due to unbound types",g)}var j=_.registeredClass.instancePrototype,B=j[i];return B===void 0||B.overloadTable===void 0&&B.className!==_.name&&B.argCount===e-2?(S.argCount=e-2,S.className=_.name,j[i]=S):(Cr(j,i,P),j[i].overloadTable[e-2]=S),Oe([],g,function(V){var te=$r(P,V,_,c,p);return j[i].overloadTable===void 0?(te.argCount=e-2,j[i]=te):j[i].overloadTable[e-2]=te,[]}),[]})}var Zt=[],Ne=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Hr(n){n>4&&--Ne[n].refcount===0&&(Ne[n]=void 0,Zt.push(n))}function cn(){for(var n=0,i=5;i<Ne.length;++i)Ne[i]!==void 0&&++n;return n}function ln(){for(var n=5;n<Ne.length;++n)if(Ne[n]!==void 0)return Ne[n];return null}function dn(){r.count_emval_handles=cn,r.get_first_emval=ln}var er={toValue:n=>(n||ae("Cannot use deleted val. handle = "+n),Ne[n].value),toHandle:n=>{switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:{var i=Zt.length?Zt.pop():Ne.length;return Ne[i]={refcount:1,value:n},i}}}};function Mr(n,i){i=Re(i),tt(n,{name:i,fromWireType:function(e){var t=er.toValue(e);return Hr(e),t},toWireType:function(e,t){return er.toHandle(t)},argPackAdvance:8,readValueFromPointer:ft,destructorFunction:null})}function pr(n){if(n===null)return"null";var i=typeof n;return i==="object"||i==="array"||i==="function"?n.toString():""+n}function pn(n,i){switch(i){case 2:return function(e){return this.fromWireType(ie[e>>2])};case 3:return function(e){return this.fromWireType(he[e>>3])};default:throw new TypeError("Unknown float type: "+n)}}function vn(n,i,e){var t=Ut(e);i=Re(i),tt(n,{name:i,fromWireType:function(o){return o},toWireType:function(o,c){return c},argPackAdvance:8,readValueFromPointer:pn(i,t),destructorFunction:null})}function hn(n,i,e,t,o,c){var p=dr(i,e);n=Re(n),o=We(t,o),it(n,function(){ke("Cannot call "+n+" due to unbound types",p)},i-1),Oe([],p,function(v){var g=[v[0],null].concat(v.slice(1));return cr(n,$r(n,g,null,o,c),i-1),[]})}function gn(n,i,e){switch(i){case 0:return e?function(o){return me[o]}:function(o){return le[o]};case 1:return e?function(o){return ce[o>>1]}:function(o){return ue[o>>1]};case 2:return e?function(o){return ne[o>>2]}:function(o){return oe[o>>2]};default:throw new TypeError("Unknown integer type: "+n)}}function yn(n,i,e,t,o){i=Re(i);var c=Ut(e),p=S=>S;if(t===0){var v=32-8*e;p=S=>S<<v>>>v}var g=i.includes("unsigned"),_=(S,j)=>{},P;g?P=function(S,j){return _(j,this.name),j>>>0}:P=function(S,j){return _(j,this.name),j},tt(n,{name:i,fromWireType:p,toWireType:P,argPackAdvance:8,readValueFromPointer:gn(i,c,t!==0),destructorFunction:null})}function _n(n,i,e){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],o=t[i];function c(p){p=p>>2;var v=oe,g=v[p],_=v[p+1];return new o(Xe,_,g)}e=Re(e),tt(n,{name:e,fromWireType:c,argPackAdvance:8,readValueFromPointer:c},{ignoreDuplicateRegistrations:!0})}function mn(n,i){i=Re(i);var e=i==="std::string";tt(n,{name:i,fromWireType:function(t){var o=oe[t>>2],c=t+4,p;if(e)for(var v=c,g=0;g<=o;++g){var _=c+g;if(g==o||le[_]==0){var P=_-v,S=tr(v,P);p===void 0?p=S:(p+="\0",p+=S),v=_+1}}else{for(var j=new Array(o),g=0;g<o;++g)j[g]=String.fromCharCode(le[c+g]);p=j.join("")}return h(t),p},toWireType:function(t,o){o instanceof ArrayBuffer&&(o=new Uint8Array(o));var c,p=typeof o=="string";p||o instanceof Uint8Array||o instanceof Uint8ClampedArray||o instanceof Int8Array||ae("Cannot pass non-string to std::string"),e&&p?c=nr(o):c=o.length;var v=u(4+c+1),g=v+4;if(oe[v>>2]=c,e&&p)hr(o,g,c+1);else if(p)for(var _=0;_<c;++_){var P=o.charCodeAt(_);P>255&&(h(g),ae("String has UTF-16 code units that do not fit in 8 bits")),le[g+_]=P}else for(var _=0;_<c;++_)le[g+_]=o[_];return t!==null&&t.push(h,v),v},argPackAdvance:8,readValueFromPointer:ft,destructorFunction:function(t){h(t)}})}var Lr=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function wn(n,i){for(var e=n,t=e>>1,o=t+i/2;!(t>=o)&&ue[t];)++t;if(e=t<<1,e-n>32&&Lr)return Lr.decode(le.subarray(n,e));for(var c="",p=0;!(p>=i/2);++p){var v=ce[n+p*2>>1];if(v==0)break;c+=String.fromCharCode(v)}return c}function bn(n,i,e){if(e===void 0&&(e=2147483647),e<2)return 0;e-=2;for(var t=i,o=e<n.length*2?e/2:n.length,c=0;c<o;++c){var p=n.charCodeAt(c);ce[i>>1]=p,i+=2}return ce[i>>1]=0,i-t}function Vr(n){return n.length*2}function Br(n,i){for(var e=0,t="";!(e>=i/4);){var o=ne[n+e*4>>2];if(o==0)break;if(++e,o>=65536){var c=o-65536;t+=String.fromCharCode(55296|c>>10,56320|c&1023)}else t+=String.fromCharCode(o)}return t}function Pn(n,i,e){if(e===void 0&&(e=2147483647),e<4)return 0;for(var t=i,o=t+e-4,c=0;c<n.length;++c){var p=n.charCodeAt(c);if(p>=55296&&p<=57343){var v=n.charCodeAt(++c);p=65536+((p&1023)<<10)|v&1023}if(ne[i>>2]=p,i+=4,i+4>o)break}return ne[i>>2]=0,i-t}function Rr(n){for(var i=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&++e,i+=4}return i}function Nr(n,i,e){e=Re(e);var t,o,c,p,v;i===2?(t=wn,o=bn,p=Vr,c=()=>ue,v=1):i===4&&(t=Br,o=Pn,p=Rr,c=()=>oe,v=2),tt(n,{name:e,fromWireType:function(g){for(var _=oe[g>>2],P=c(),S,j=g+4,B=0;B<=_;++B){var V=g+4+B*i;if(B==_||P[V>>v]==0){var te=V-j,Z=t(j,te);S===void 0?S=Z:(S+="\0",S+=Z),j=V+i}}return h(g),S},toWireType:function(g,_){typeof _!="string"&&ae("Cannot pass non-string to C++ string type "+e);var P=p(_),S=u(4+P+i);return oe[S>>2]=P>>v,o(_,S+4,P+i),g!==null&&g.push(h,S),S},argPackAdvance:8,readValueFromPointer:ft,destructorFunction:function(g){h(g)}})}function zr(n,i,e,t,o,c){Ze[n]={name:Re(i),rawConstructor:We(e,t),rawDestructor:We(o,c),fields:[]}}function Er(n,i,e,t,o,c,p,v,g,_){Ze[n].fields.push({fieldName:Re(i),getterReturnType:e,getter:We(t,o),getterContext:c,setterArgumentType:p,setter:We(v,g),setterContext:_})}function Tn(n,i){i=Re(i),tt(n,{isVoid:!0,name:i,argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})}function qr(n){n>4&&(Ne[n].refcount+=1)}function Cn(n,i){var e=Ie[n];return e===void 0&&ae(i+" has unknown type "+Qt(n)),e}function Gr(n,i){n=Cn(n,"_emval_take_value");var e=n.readValueFromPointer(i);return er.toHandle(e)}function An(){qt("")}function $n(n,i,e){le.copyWithin(n,i,i+e)}function Rn(){return 2147483648}function En(n){try{return _e.grow(n-Xe.byteLength+65535>>>16),Vt(_e.buffer),1}catch{}}function Sr(n){var i=le.length;n=n>>>0;var e=Rn();if(n>e)return!1;let t=(g,_)=>g+(_-g%_)%_;for(var o=1;o<=4;o*=2){var c=i*(1+.2/o);c=Math.min(c,n+100663296);var p=Math.min(e,t(Math.max(n,c),65536)),v=En(p);if(v)return!0}return!1}function kr(n){return 52}function Et(n,i,e,t,o){return 70}var Ht=[null,[],[]];function _t(n,i){var e=Ht[n];i===0||i===10?((n===1?we:ye)(St(e,0)),e.length=0):e.push(i)}function Jr(n,i,e,t){for(var o=0,c=0;c<e;c++){var p=oe[i>>2],v=oe[i+4>>2];i+=8;for(var g=0;g<v;g++)_t(n,le[p+g]);o+=v}return oe[t>>2]=o,0}function Xr(n){return n}function Dr(n){var i=r["_"+n];return i}function Yr(n,i){me.set(n,i)}function vr(n,i,e,t,o){var c={string:V=>{var te=0;if(V!=null&&V!==0){var Z=(V.length<<2)+1;te=R(Z),hr(V,te,Z)}return te},array:V=>{var te=R(V.length);return Yr(V,te),te}};function p(V){return i==="string"?tr(V):i==="boolean"?!!V:V}var v=Dr(n),g=[],_=0;if(t)for(var P=0;P<t.length;P++){var S=c[e[P]];S?(_===0&&(_=k()),g[P]=S(t[P])):g[P]=t[P]}var j=v.apply(null,g);function B(V){return _!==0&&D(_),p(V)}return j=B(j),j}re=r.InternalError=de(Error,"InternalError"),Xt(),mt=r.BindingError=de(Error,"BindingError"),fn(),bt(),Ar(),Ge=r.UnboundTypeError=de(Error,"UnboundTypeError"),dn();var Kr={L:Ur,v:ze,c:mr,k:Jt,i:Ft,f:Ot,A:wr,D:br,I:jt,y:Je,x:ot,a:Tt,H:Mr,u:vn,o:hn,h:yn,d:_n,t:mn,q:Nr,p:zr,B:Er,J:Tn,N:Hr,O:qr,n:Gr,r:An,G:$n,E:Sr,F:kr,C:Et,s:Jr,b:J,z:G,w:T,e:s,l:w,m:a,j:d,K:U,g:K,M:Xr};Fr(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.Q).apply(null,arguments)};var u=r._malloc=function(){return(u=r._malloc=r.asm.R).apply(null,arguments)},f=r.___cxa_free_exception=function(){return(f=r.___cxa_free_exception=r.asm.S).apply(null,arguments)},h=r._free=function(){return(h=r._free=r.asm.U).apply(null,arguments)},b=r.___getTypeName=function(){return(b=r.___getTypeName=r.asm.V).apply(null,arguments)};r.__embind_initialize_bindings=function(){return(r.__embind_initialize_bindings=r.asm.W).apply(null,arguments)};var C=r.setTempRet0=function(){return(C=r.setTempRet0=r.asm.X).apply(null,arguments)},k=r.stackSave=function(){return(k=r.stackSave=r.asm.Y).apply(null,arguments)},D=r.stackRestore=function(){return(D=r.stackRestore=r.asm.Z).apply(null,arguments)},R=r.stackAlloc=function(){return(R=r.stackAlloc=r.asm._).apply(null,arguments)},F=r.___cxa_can_catch=function(){return(F=r.___cxa_can_catch=r.asm.$).apply(null,arguments)},x=r.___cxa_is_pointer_type=function(){return(x=r.___cxa_is_pointer_type=r.asm.aa).apply(null,arguments)};r.dynCall_ji=function(){return(r.dynCall_ji=r.asm.ba).apply(null,arguments)},r.dynCall_iiji=function(){return(r.dynCall_iiji=r.asm.ca).apply(null,arguments)},r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.da).apply(null,arguments)};function G(n,i,e){var t=k();try{return ge(n)(i,e)}catch(o){if(D(t),o!==o+0)throw o;_setThrew(1,0)}}function J(n,i){var e=k();try{return ge(n)(i)}catch(t){if(D(e),t!==t+0)throw t;_setThrew(1,0)}}function K(n,i,e,t,o,c,p,v,g,_,P){var S=k();try{ge(n)(i,e,t,o,c,p,v,g,_,P)}catch(j){if(D(S),j!==j+0)throw j;_setThrew(1,0)}}function s(n,i){var e=k();try{ge(n)(i)}catch(t){if(D(e),t!==t+0)throw t;_setThrew(1,0)}}function a(n,i,e,t){var o=k();try{ge(n)(i,e,t)}catch(c){if(D(o),c!==c+0)throw c;_setThrew(1,0)}}function d(n,i,e,t,o){var c=k();try{ge(n)(i,e,t,o)}catch(p){if(D(c),p!==p+0)throw p;_setThrew(1,0)}}function w(n,i,e){var t=k();try{ge(n)(i,e)}catch(o){if(D(t),o!==o+0)throw o;_setThrew(1,0)}}function T(n,i,e,t){var o=k();try{return ge(n)(i,e,t)}catch(c){if(D(o),c!==c+0)throw c;_setThrew(1,0)}}function U(n,i,e,t,o,c,p){var v=k();try{ge(n)(i,e,t,o,c,p)}catch(g){if(D(v),g!==g+0)throw g;_setThrew(1,0)}}r.ccall=vr;var O;Ke=function n(){O||E(),O||(Ke=n)};function E(n){if(Ye>0||(gr(),Ye>0))return;function i(){O||(O=!0,r.calledRun=!0,!$e&&(Or(),H(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),Zr()))}r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),i()},1)):i()}if(r.preInit)for(typeof r.preInit=="function"&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return E(),r.ready}})();l.exports=m}(Yn)),Yn.exports}var go=ho(),yo=zn(go);const _o=new URL("/assets/openjphjs-CANpALFP.wasm",import.meta.url),In={codec:void 0,decoder:void 0,decodeConfig:{}};function mo(l,y,m){const $={width:y,height:m};for(;l>0;)$.width=Math.ceil($.width/2),$.height=Math.ceil($.height/2),l--;return $}function wo(l){if(In.decodeConfig=l,In.codec)return Promise.resolve();const y=yo({locateFile:m=>m.endsWith(".wasm")?_o.toString():m});return new Promise((m,$)=>{y.then(A=>{In.codec=A,In.decoder=new A.HTJ2KDecoder,m()},$)})}async function bo(l,y){await wo();const m=new In.codec.HTJ2KDecoder,$=m.getEncodedBuffer(l.length);$.set(l);const A=y.decodeLevel||0;m.decodeSubResolution(A);const r=m.getFrameInfo();if(y.decodeLevel>0){const{width:Lt,height:St}=mo(y.decodeLevel,r.width,r.height);r.width=Lt,r.height=St}const H=m.getDecodedBuffer();new Uint8Array(H.length).set(H);const W=`x: ${m.getImageOffset().x}, y: ${m.getImageOffset().y}`,q=m.getNumDecompositions(),L=m.getNumLayers(),N=["unknown","LRCP","RLCP","RPCL","PCRL","CPRL"][m.getProgressionOrder()+1],M=m.getIsReversible(),X=`${m.getBlockDimensions().width} x ${m.getBlockDimensions().height}`,z=`${m.getTileSize().width} x ${m.getTileSize().height}`,ee=`${m.getTileOffset().x}, ${m.getTileOffset().y}`,fe=`${H.length.toLocaleString()} bytes`,Ae=`${(H.length/$.length).toFixed(2)}:1`,pe={columns:r.width,rows:r.height,bitsPerPixel:r.bitsPerSample,signed:r.isSigned,bytesPerPixel:y.bytesPerPixel,componentsPerPixel:r.componentCount};let we=Po(r,H);const{buffer:ye,byteOffset:se,byteLength:_e}=we,$e=ye.slice(se,se+_e);we=new we.constructor($e);const at={imageOffset:W,numDecompositions:q,numLayers:L,progessionOrder:N,reversible:M,blockDimensions:X,tileSize:z,tileOffset:ee,decodedSize:fe,compressionRatio:Ae};return{...y,pixelData:we,imageInfo:pe,encodeOptions:at,...at,...pe}}function Po(l,y){return l.bitsPerSample>8?l.isSigned?new Int16Array(y.buffer,y.byteOffset,y.byteLength/2):new Uint16Array(y.buffer,y.byteOffset,y.byteLength/2):l.isSigned?new Int8Array(y.buffer,y.byteOffset,y.byteLength):new Uint8Array(y.buffer,y.byteOffset,y.byteLength)}function To(l,y){const m=l.length,{rescaleSlope:$,rescaleIntercept:A,suvbw:r,doseGridScaling:H}=y;if(y.modality==="PT"&&typeof r=="number"&&!isNaN(r))for(let I=0;I<m;I++)l[I]=r*(l[I]*$+A);else if(y.modality==="RTDOSE"&&typeof H=="number"&&!isNaN(H))for(let I=0;I<m;I++)l[I]=l[I]*H;else for(let I=0;I<m;I++)l[I]=l[I]*$+A;return!0}function Co(l){let y=l[0],m=l[0],$;const A=l.length;for(let r=1;r<A;r++)$=l[r],y=Math.min(y,$),m=Math.max(m,$);return{min:y,max:m}}function _i(l,y){let m;return Number.isInteger(l)&&Number.isInteger(y)&&(l>=0?y<=255?m=Uint8Array:y<=65535?m=Uint16Array:y<=4294967295&&(m=Uint32Array):l>=-128&&y<=127?m=Int8Array:l>=-32768&&y<=32767&&(m=Int16Array)),m||Float32Array}function Ao(l,y,m){return _i(l,y)===m}function $o(l){return l==="RGB"||l==="PALETTE COLOR"||l==="YBR_FULL"||l==="YBR_FULL_422"||l==="YBR_PARTIAL_422"||l==="YBR_PARTIAL_420"||l==="YBR_RCT"||l==="YBR_ICT"}const Ro={bilinear:bi,replicate:Pi},li={Uint8Array,Uint16Array,Int16Array,Float32Array,Uint32Array};function Eo(l,y,m,$){var pe,we,ye;const A=l.pixelRepresentation!==void 0&&l.pixelRepresentation===1,r=A&&l.bitsStored!==void 0?32-l.bitsStored:void 0;if(A&&r!==void 0)for(let se=0;se<l.pixelData.length;se++)l.pixelData[se]=l.pixelData[se]<<r>>r;let H=l.pixelData;l.pixelDataLength=l.pixelData.length;const{min:I,max:W}=Co(l.pixelData),q=typeof y.allowFloatRendering<"u"?y.allowFloatRendering:!0;let L=$o(l.photometricInterpretation)&&((pe=y.targetBuffer)==null?void 0:pe.offset)===void 0;const M=((we=y.preScale)==null?void 0:we.enabled)&&Object.values(y.preScale.scalingParameters).some(se=>typeof se=="number"&&!Number.isInteger(se)),X=!y.preScale.enabled||!q&&M,z=(ye=y.targetBuffer)==null?void 0:ye.type;if(z&&y.preScale.enabled&&!X){const se=y.preScale.scalingParameters,_e=Qn(I,W,se);L=!Ao(_e.min,_e.max,li[z])}z&&!L?H=ko(y,l,li,H):y.preScale.enabled&&!X?H=Do(y,I,W,l):H=mi(I,W,l);let ee=I,fe=W;if(y.preScale.enabled&&!X){const se=y.preScale.scalingParameters;if(wi(se),So(se)){To(H,se),l.preScale={...y.preScale,scaled:!0};const $e=Qn(I,W,se);ee=$e.min,fe=$e.max}}else X&&(l.preScale={enabled:!0,scaled:!1},ee=I,fe=W);l.pixelData=H,l.smallestPixelValue=ee,l.largestPixelValue=fe;const Ae=new Date().getTime();return l.decodeTimeInMS=Ae-m,l}function So(l){const{rescaleSlope:y,rescaleIntercept:m,modality:$,doseGridScaling:A,suvbw:r}=l;return typeof y=="number"&&typeof m=="number"||$==="RTDOSE"&&typeof A=="number"||$==="PT"&&typeof r=="number"}function ko(l,y,m,$){const{arrayBuffer:A,type:r,offset:H=0,length:I,rows:W}=l.targetBuffer,q=m[r];if(!q)throw new Error(`target array ${r} is not supported, or doesn't exist.`);W&&W!=y.rows&&Fo(y,l.targetBuffer,q);const L=y.pixelDataLength,N=H,M=I??L-N,X=y.pixelData;if(M!==X.length)throw new Error(`target array for image does not have the same length (${M}) as the decoded image length (${X.length}).`);const z=A?new q(A,N,M):new q(M);return z.set(X,0),$=z,$}function Do(l,y,m,$){const A=l.preScale.scalingParameters;wi(A);const r=Qn(y,m,A);return mi(r.min,r.max,$)}function mi(l,y,m){const $=_i(l,y),A=new $(m.pixelData.length);return A.set(m.pixelData,0),A}function Qn(l,y,m){const{rescaleSlope:$,rescaleIntercept:A,modality:r,doseGridScaling:H,suvbw:I}=m;return r==="PT"&&typeof I=="number"&&!isNaN(I)?{min:I*(l*$+A),max:I*(y*$+A)}:r==="RTDOSE"&&typeof H=="number"&&!isNaN(H)?{min:l*H,max:y*H}:typeof $=="number"&&typeof A=="number"?{min:$*l+A,max:$*y+A}:{min:l,max:y}}function wi(l){if(!l)throw new Error("options.preScale.scalingParameters must be defined if preScale.enabled is true, and scalingParameters cannot be derived from the metadata providers.")}function Oo(l,y,m){const{samplesPerPixel:$}=l,{rows:A,columns:r}=y,H=A*r*$,I=new m(H),W=I.byteLength/H;return{pixelData:I,rows:A,columns:r,frameInfo:{...l.frameInfo,rows:A,columns:r},imageInfo:{...l.imageInfo,rows:A,columns:r,bytesPerPixel:W}}}function Fo(l,y,m){const $=Oo(l,y,m),{scalingType:A="replicate"}=y;return Ro[A](l,$),Object.assign(l,$),l.pixelDataLength=l.pixelData.length,l}async function Uo(l,y,m,$,A,r){const H=new Date().getTime();let I=null,W;switch(y){case"1.2.840.10008.1.2":case"1.2.840.10008.1.2.1":I=ni(l,m);break;case"1.2.840.10008.1.2.2":I=xi(l,m);break;case"1.2.840.10008.1.2.1.99":I=ni(l,m);break;case"1.2.840.10008.1.2.5":I=Wi(l,m);break;case"1.2.840.10008.1.2.4.50":W={...l},I=Yi(m,W);break;case"1.2.840.10008.1.2.4.51":I=Zi(l,m);break;case"1.2.840.10008.1.2.4.57":I=oi(l,m);break;case"1.2.840.10008.1.2.4.70":I=oi(l,m);break;case"1.2.840.10008.1.2.4.80":W={signed:l.pixelRepresentation===1,bytesPerPixel:l.bitsAllocated<=8?1:2,...l},I=si(m,W);break;case"1.2.840.10008.1.2.4.81":W={signed:l.pixelRepresentation===1,bytesPerPixel:l.bitsAllocated<=8?1:2,...l},I=si(m,W);break;case"1.2.840.10008.1.2.4.90":W={...l},I=fi(m,W);break;case"1.2.840.10008.1.2.4.91":W={...l},I=fi(m,W);break;case"3.2.840.10008.1.2.4.96":case"1.2.840.10008.1.2.4.201":case"1.2.840.10008.1.2.4.202":case"1.2.840.10008.1.2.4.203":W={...l},I=bo(m,W);break;default:throw new Error(`no decoder for transfer syntax ${y}`)}if(!I)throw new Error("decodePromise not defined");const q=await I,L=Eo(q,A,H);return r==null||r(L),L}const Io={decodeTask({imageFrame:l,transferSyntax:y,decodeConfig:m,options:$,pixelData:A,callbackFn:r}){return Uo(l,y,A,m,$,r)}};Zn(Io);
