import type { Canvas } from "fabric";

/*
Updates the font family of selected text objects.
Only applies to textbox objects.
*/
export const changeSelectedObjectsFontFamily = (canvas: Canvas, fontFamily: string): void => {
  const selectedObject = canvas.getActiveObject();
  if (!selectedObject) return;

  const selectedObjects =
    selectedObject.type === "activeselection"
      ? (selectedObject as any).getObjects()
      : [selectedObject];

  selectedObjects.forEach((obj: any) => {
    if (obj.type === "textbox") {
      obj.set({ fontFamily });
    }
  });

  canvas.renderAll();
};

/*
Gets the current font family from a selected object.
Returns the font family if it's a text object, undefined otherwise.
*/
export const getObjectFontFamily = (obj: any): string | undefined => {
  if (!obj) return undefined;

  if (obj.type === "textbox" && obj.fontFamily) {
    return obj.fontFamily;
  }

  return undefined;
};

/*
Context-aware font family change handler.
If object is selected: only update the selected object.
If nothing is selected: call the default change handler.
*/
export const applyFontFamilyContextAware = (
  canvas: Canvas,
  fontFamily: string,
  onDefaultChange?: (fontFamily: string) => void
): void => {
  const selectedObject = canvas.getActiveObject();
  
  if (selectedObject) {
    changeSelectedObjectsFontFamily(canvas, fontFamily);
  } else {
    onDefaultChange?.(fontFamily);
  }
};
