import { Canvas } from "fabric";
import { updateArrowHeadSize } from "./arrows";

/*
Updates stroke width for selected objects on the canvas.
Handles different object types and excludes objects that shouldn't have stroke width changes.
*/
export const changeSelectedObjectsStrokeWidth = (canvas: Canvas, strokeWidth: number): void => {
  const activeObject = canvas.getActiveObject();

  if (!activeObject) return;

  const selectedObjects =
    activeObject.type === "activeselection" ? (activeObject as any).getObjects() : [activeObject];

  selectedObjects.forEach((obj: any) => {
    // Exclude textbox, cropRect, protractor, and highlight from stroke width changes
    if (
      obj.type !== "textbox" &&
      obj.name !== "cropRect" &&
      obj.name !== "protractor" &&
      obj.name !== "highlight"
    ) {
      obj.set({ strokeWidth: strokeWidth });

      // Special handling for arrows - update head size proportionally
      if (obj.name === "arrow" || obj.customType === "arrow") {
        updateArrowHeadSize(obj, strokeWidth);
      }
    }
  });

  canvas.renderAll();
};

/*
Gets the current stroke width from a selected object.
Returns undefined if object doesn't have stroke width or shouldn't be affected.
*/
export const getObjectStrokeWidth = (obj: any): number | undefined => {
  if (!obj) return undefined;

  // Exclude objects that don't use stroke width
  if (
    obj.type === "textbox" ||
    obj.name === "cropRect" ||
    obj.name === "protractor" ||
    obj.name === "highlight"
  ) {
    return undefined;
  }

  return obj.strokeWidth;
};

/*
Context-aware stroke width update based on selection state.
Selected object: changes only that object's stroke width.
No selection: calls onDefaultChange callback for session storage update.
*/
export const applyStrokeWidthContextAware = (
  canvas: Canvas,
  width: number,
  onDefaultChange?: (width: number) => void
): void => {
  const selectedObject = canvas.getActiveObject();

  if (selectedObject) {
    changeSelectedObjectsStrokeWidth(canvas, width);
  } else {
    onDefaultChange?.(width);
  }
};
