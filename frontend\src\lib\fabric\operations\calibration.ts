import { Canvas } from "fabric";
import type { CalibrationData } from "@/models";

const CALIBRATION_KEYS = {
  DISTANCE: "calibrationDistance",
  PIXEL_LENGTH: "calibratedPixelLength",
  IMAGE_SCALE: "calibrationImageScale",
};

export const saveCalibrationToLocalStorage = (calibrationData: CalibrationData) => {
  localStorage.setItem(CALIBRATION_KEYS.DISTANCE, calibrationData.calibrationDistance.toString());
  localStorage.setItem(
    CALIBRATION_KEYS.PIXEL_LENGTH,
    calibrationData.calibratedPixelLength.toString()
  );
  localStorage.setItem(
    CALIBRATION_KEYS.IMAGE_SCALE,
    calibrationData.calibrationImageScale.toString()
  );
};

export const getCalibrationFromLocalStorage = (): CalibrationData | null => {
  const distance = localStorage.getItem(CALIBRATION_KEYS.DISTANCE);
  const pixelLength = localStorage.getItem(CALIBRATION_KEYS.PIXEL_LENGTH);
  const imageScale = localStorage.getItem(CALIBRATION_KEYS.IMAGE_SCALE);

  if (!distance || !pixelLength || !imageScale) {
    return null;
  }

  return {
    calibrationDistance: parseFloat(distance),
    calibratedPixelLength: parseFloat(pixelLength),
    calibrationImageScale: parseFloat(imageScale),
  };
};

export const clearCalibrationFromLocalStorage = () => {
  localStorage.removeItem(CALIBRATION_KEYS.DISTANCE);
  localStorage.removeItem(CALIBRATION_KEYS.PIXEL_LENGTH);
  localStorage.removeItem(CALIBRATION_KEYS.IMAGE_SCALE);
};
/*
Creates a submit handler that reads two calibration points from the canvas,
computes pixel distance and saves calibration data in localStorage.
*/

export const createCalibrationSubmitHandler = (
  canvas: Canvas | null,
  onCalibrationComplete?: () => void,
  disableUndoTracking?: () => void,
  enableUndoTracking?: () => void
) => {
  return (value: number) => {
    if (!canvas) return;

    const calibrationPoints = canvas
      .getObjects("rect")
      .filter((obj) => (obj as any).name === "calibrateLine");

    if (calibrationPoints.length !== 2) {
      alert("Please mark exactly two points for calibration.");
      return;
    }

    const [point1, point2] = calibrationPoints;

    const x1 = point1.left!;
    const y1 = point1.top!;
    const x2 = point2.left!;
    const y2 = point2.top!;

    const distanceInPixels = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);

    const bgImage = canvas.backgroundImage;
    const imageScale = bgImage?.scaleX ?? 1;

    const calibrationData: CalibrationData = {
      calibrationDistance: value,
      calibratedPixelLength: distanceInPixels,
      calibrationImageScale: imageScale,
    };

    saveCalibrationToLocalStorage(calibrationData);
    disableUndoTracking?.();
    calibrationPoints.forEach((point) => canvas.remove(point));
    canvas.renderAll();
    enableUndoTracking?.();

    alert(`Calibration distance of ${value} mm saved.`);

    onCalibrationComplete?.();
  };
};

/*
Closes the calibration modal and removes any calibration markers from the canvas.
*/

export const createCalibrationCloseHandler = (
  canvas: Canvas | null,
  setCalibrationModalOpen: (open: boolean) => void,
  disableUndoTracking?: () => void,
  enableUndoTracking?: () => void
) => {
  return () => {
    setCalibrationModalOpen(false);

    if (!canvas) return;

    const calibrationPoints = canvas
      .getObjects("rect")
      .filter((obj) => (obj as any).name === "calibrateLine");
    disableUndoTracking?.();
    calibrationPoints.forEach((p) => canvas.remove(p));
    canvas.renderAll();
    enableUndoTracking?.();
  };
};
