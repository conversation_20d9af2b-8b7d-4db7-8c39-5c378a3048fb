import { Textbox } from "fabric";
import { v4 as uuidv4 } from "uuid";

/*
Creates textbox with locked scaling controls to prevent unwanted resizing.
Uses custom toObject method to preserve control properties across save/load.
*/
export const createTextbox = (pointer: { x: number; y: number }, config: any): Textbox => {
  const textbox = new Textbox("Text", {
    left: pointer.x,
    top: pointer.y,
    id: uuidv4(),
    hasControls: false,
    hasBorders: true,
    lockScalingX: true,
    lockScalingY: true,
    lockUniScaling: true,
    ...config,
  });

  const originalToObject = textbox.toObject;
  (textbox as any).toObject = function (propertiesToInclude?: any[]) {
    const textProps = [
      "hasControls",
      "hasBorders",
      "lockScalingX",
      "lockScalingY",
      "lockUniScaling",
    ];
    const allProps = propertiesToInclude ? [...propertiesToInclude, ...textProps] : textProps;
    return originalToObject.call(this, allProps);
  };

  return textbox;
};

/*
Restores text control properties after loading from JSON.
Fabric.js doesn't properly restore custom control properties on reload.
*/
export const restoreTextRender = (textObj: any) => {
  textObj.set({
    hasControls: false,
    hasBorders: true,
    lockScalingX: true,
    lockScalingY: true,
    lockUniScaling: true,
  });

  const originalToObject = textObj.toObject;
  textObj.toObject = function (propertiesToInclude?: any[]) {
    const textProps = [
      "hasControls",
      "hasBorders",
      "lockScalingX",
      "lockScalingY",
      "lockUniScaling",
    ];
    const allProps = propertiesToInclude ? [...propertiesToInclude, ...textProps] : textProps;
    return originalToObject.call(this, allProps);
  };
};

export const isTextbox = (obj: any): obj is Textbox => {
  return obj?.type === "textbox";
};
