import React, { useState } from "react";
import {
  Button,
  ButtonGroup,
  Navbar,
  NavbarGroup,
  NavbarHeading,
  Alignment,
} from "@blueprintjs/core";
import { FaCog, FaEye, FaEyeSlash, FaHome } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

interface AppToolbarProps {
  showAnnotationControls?: boolean;
}

const AppToolbar: React.FC<AppToolbarProps> = ({ showAnnotationControls = false }) => {
  const navigate = useNavigate();
  const [isMainToolbarVisible, setIsMainToolbarVisible] = useState<boolean>(true);
  const [isAnnotationPropertiesVisible, setIsAnnotationPropertiesVisible] =
    useState<boolean>(false);

  const handleToggleMainToolbar = () => {
    setIsMainToolbarVisible(!isMainToolbarVisible);
    // Dispatch custom event to notify viewers
    window.dispatchEvent(
      new CustomEvent("toggleMainToolbar", {
        detail: { visible: !isMainToolbarVisible },
      })
    );
  };

  const handleShowAnnotationProperties = () => {
    setIsAnnotationPropertiesVisible(!isAnnotationPropertiesVisible);
    // Dispatch custom event to notify viewers
    window.dispatchEvent(
      new CustomEvent("toggleAnnotationProperties", {
        detail: { visible: !isAnnotationPropertiesVisible },
      })
    );
  };

  const handleGoHome = () => {
    navigate("/");
  };

  return (
    <Navbar>
      <NavbarGroup align={Alignment.LEFT}>
        <Button icon={<FaHome />} minimal onClick={handleGoHome} />
        <NavbarHeading style={{ marginLeft: 8 }}>Viewer</NavbarHeading>
      </NavbarGroup>
      {showAnnotationControls && (
        <NavbarGroup align={Alignment.RIGHT}>
          <ButtonGroup>
            <Button
              icon={<FaCog />}
              minimal
              onClick={handleShowAnnotationProperties}
              active={isAnnotationPropertiesVisible}
            />
            <Button
              icon={isMainToolbarVisible ? <FaEyeSlash /> : <FaEye />}
              minimal
              onClick={handleToggleMainToolbar}
            />
          </ButtonGroup>
        </NavbarGroup>
      )}
    </Navbar>
  );
};

export default AppToolbar;
