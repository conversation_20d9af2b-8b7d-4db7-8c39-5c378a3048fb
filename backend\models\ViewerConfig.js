const mongoose = require("mongoose");
const { DEFAULT_FABRIC_CONFIGS } = require("../config/defaultFabricConfigs");

const stackConfigSchema = new mongoose.Schema(
  {
    id: {
      type: String,
      required: true,
      unique: true,
      default: "stackFile",
    },
    viewer: {
      imageUrl: {
        type: String,
        required: true,
      },
      fabricConfigs: {
        brightness: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.brightness,
        },
        contrast: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.contrast,
        },
        grayscale: {
          type: Boolean,
          default: DEFAULT_FABRIC_CONFIGS.grayscale,
        },
        invert: {
          type: Boolean,
          default: DEFAULT_FABRIC_CONFIGS.invert,
        },
        sharpness: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.sharpness,
        },
        gammaR: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.gammaR,
        },
        gammaG: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.gammaG,
        },
        gammaB: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.gammaB,
        },

        annotations: {
          type: mongoose.Schema.Types.Mixed,
          default: DEFAULT_FABRIC_CONFIGS.annotations,
        },
        cropData: {
          isCropped: {
            type: Boolean,
            default: DEFAULT_FABRIC_CONFIGS.cropData.isCropped,
          },
          normalizedCropRect: {
            left: { type: Number, default: null },
            top: { type: Number, default: null },
            width: { type: Number, default: null },
            height: { type: Number, default: null },
          },
        },
        transformState: {
          rotations: {
            type: Number,
            default: DEFAULT_FABRIC_CONFIGS.transformState.rotations,
          },
          flipHorizontal: {
            type: Boolean,
            default: DEFAULT_FABRIC_CONFIGS.transformState.flipHorizontal,
          },
          flipVertical: {
            type: Boolean,
            default: DEFAULT_FABRIC_CONFIGS.transformState.flipVertical,
          },
        },
        calibrationData: {
          calibrationDistance: { type: Number, default: 0 },
          calibratedPixelLength: { type: Number, default: 0 },
          calibrationImageScale: { type: Number, default: 0 },
        },
      },
    },
  },
  {
    timestamps: true,
  }
);

const volumeConfigSchema = new mongoose.Schema(
  {
    id: {
      type: String,
      required: true,
      unique: true,
      default: "volumeFile",
    },
    viewer: {
      imageUrl: [
        {
          type: String,
          required: true,
        },
      ],
      configs: {
        shift: {
          type: Number,
          default: 400,
        },
      },
    },
  },
  {
    timestamps: true,
  }
);

const imageConfigSchema = new mongoose.Schema(
  {
    id: {
      type: String,
      required: true,
      unique: true,
      default: "imageFile",
    },
    viewer: {
      imageUrl: {
        type: String,
        required: true,
      },
      fabricConfigs: {
        brightness: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.brightness,
        },
        contrast: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.contrast,
        },
        grayscale: {
          type: Boolean,
          default: DEFAULT_FABRIC_CONFIGS.grayscale,
        },
        invert: {
          type: Boolean,
          default: DEFAULT_FABRIC_CONFIGS.invert,
        },
        sharpness: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.sharpness,
        },
        gammaR: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.gammaR,
        },
        gammaG: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.gammaG,
        },
        gammaB: {
          type: Number,
          default: DEFAULT_FABRIC_CONFIGS.gammaB,
        },

        annotations: {
          type: mongoose.Schema.Types.Mixed,
          default: DEFAULT_FABRIC_CONFIGS.annotations,
        },
        cropData: {
          isCropped: {
            type: Boolean,
            default: DEFAULT_FABRIC_CONFIGS.cropData.isCropped,
          },
          normalizedCropRect: {
            left: { type: Number, default: null },
            top: { type: Number, default: null },
            width: { type: Number, default: null },
            height: { type: Number, default: null },
          },
        },
        transformState: {
          rotations: {
            type: Number,
            default: DEFAULT_FABRIC_CONFIGS.transformState.rotations,
          },
          flipHorizontal: {
            type: Boolean,
            default: DEFAULT_FABRIC_CONFIGS.transformState.flipHorizontal,
          },
          flipVertical: {
            type: Boolean,
            default: DEFAULT_FABRIC_CONFIGS.transformState.flipVertical,
          },
        },
        calibrationData: {
          calibrationDistance: { type: Number, default: 0 },
          calibratedPixelLength: { type: Number, default: 0 },
          calibrationImageScale: { type: Number, default: 0 },
        },
      },
    },
  },
  {
    timestamps: true,
  }
);

const StackConfig = mongoose.model("StackConfig", stackConfigSchema);
const VolumeConfig = mongoose.model("VolumeConfig", volumeConfigSchema);
const ImageConfig = mongoose.model("ImageConfig", imageConfigSchema);

module.exports = {
  StackConfig,
  VolumeConfig,
  ImageConfig,
};
