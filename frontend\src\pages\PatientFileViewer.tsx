import { useState, Suspense, lazy } from "react";
import { useLoaderData } from "react-router-dom";
import type { FileItem, PatientDetails, VolumeData, ImageData } from "@/models";
import { apiGetStackData, apiGetVolumeData, apiGetImageData } from "@/services/api";
import AppToolbar from "@/components/toolbar/AppToolbar";
import { Card, Menu, MenuItem, Tag, NonIdealState, Spinner } from "@blueprintjs/core";

const Dicom2DViewer = lazy(() => import("@/components/viewers/Dicom2DViewer"));
const Dicom3DViewer = lazy(() => import("@/components/viewers/Dicom3DViewer"));
const ImageViewer = lazy(() => import("@/components/viewers/ImageViewer"));

const PatientFileViewer: React.FC = () => {
  const patientData = useLoaderData() as PatientDetails;
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [viewerData, setViewerData] = useState<ImageData | VolumeData | null>(null);
  const [loading, setLoading] = useState(false);

  const handleFileClick = async (file: FileItem) => {
    setSelectedFile(file);
    setLoading(true);
    if (file.type === "image") setViewerData(await apiGetImageData(file.id));
    else if (file.type === "volume") setViewerData(await apiGetVolumeData(file.id));
    else if (file.type === "stack") setViewerData(await apiGetStackData(file.id));
    setLoading(false);
  };

  const renderViewer = () => {
    if (!selectedFile || !viewerData) return null;
    if (selectedFile.type === "image")
      return <ImageViewer key={selectedFile.id} data={viewerData as ImageData} />;
    if (selectedFile.type === "volume")
      return <Dicom3DViewer key={selectedFile.id} data={viewerData as VolumeData} />;
    if (selectedFile.type === "stack")
      return <Dicom2DViewer key={selectedFile.id} data={viewerData as ImageData} />;
    return null;
  };

  return (
    <div className="patient-viewer">
      <AppToolbar showAnnotationControls={true} />
      <div className="patient-viewer-content">
        <Card
          elevation={1}
          style={{
            width: 280,
            minWidth: 280,
            maxWidth: 280,
            height: "100%",
            borderRight: "1px solid var(--pt-divider-black, #d9d9d9)",
            padding: 0,
          }}
        >
          <div
            style={{
              padding: 12,
              borderBottom: "1px solid var(--pt-divider-black, #d9d9d9)",
              background: "var(--pt-app-top-background-color, #f5f8fa)",
            }}
          >
            <h3 className="bp5-heading" style={{ margin: 0 }}>
              {patientData.patientName}
            </h3>
            <div style={{ opacity: 0.8 }}>ID: {patientData.patientId}</div>
          </div>
          <div
            style={{
              padding: 8,
              borderBottom: "1px solid var(--pt-divider-black, #d9d9d9)",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <h4 className="bp5-heading" style={{ margin: 0 }}>
              Files
            </h4>
            <Tag minimal>{patientData.files.length}</Tag>
          </div>
          <Menu style={{ overflowY: "auto", height: "calc(100% - 100px)" }}>
            {patientData.files.map((file) => (
              <MenuItem
                key={file.id}
                text={file.name}
                active={selectedFile?.id === file.id}
                labelElement={
                  <Tag minimal intent={selectedFile?.id === file.id ? "primary" : "none"}>
                    {file.type}
                  </Tag>
                }
                onClick={() => handleFileClick(file)}
              />
            ))}
          </Menu>
        </Card>

        <div className="viewer-panel">
          {!selectedFile ? (
            <NonIdealState
              icon="document"
              title="Select a file to view"
              description="Choose a file from the list to load it in the viewer"
            />
          ) : loading ? (
            <div className="loading-state">
              <Spinner />
            </div>
          ) : viewerData ? (
            <Suspense
              fallback={
                <div className="loading-state">
                  <Spinner />
                </div>
              }
            >
              {renderViewer()}
            </Suspense>
          ) : (
            <NonIdealState
              icon="error"
              title="Failed to load file"
              description={`Could not load ${selectedFile.name}`}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientFileViewer;
