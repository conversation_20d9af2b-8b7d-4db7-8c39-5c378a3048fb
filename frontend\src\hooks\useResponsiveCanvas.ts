import { Canvas } from "fabric";
import {
  applyCropFromNormalized,
  calculateFittedCanvasDimensions,
  scaleCanvasObjects,
  shouldResize,
} from "@/lib/fabric/operations";
import { useCallback, useEffect, useRef } from "react";
import type { UseResponsiveCanvasProps, CropData } from "@/models";
import { syncMagnifierOnCanvasChange } from "@/lib/fabric/operations/magnifier";

/** ResizeObserver-driven canvas resize preserving aspect ratio and crop */

const handleCanvasResize = (canvas: Canvas, container: HTMLElement, cropData?: CropData) => {
  const containerRect = container.getBoundingClientRect();
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  let targetWidth: number;
  let targetHeight: number;

  if (cropData?.isCropped && cropData.normalizedCropRect) {
    const cropAspectRatio = currentWidth / currentHeight;
    const containerAspectRatio = containerRect.width / containerRect.height;

    if (cropAspectRatio > containerAspectRatio) {
      targetWidth = containerRect.width;
      targetHeight = containerRect.width / cropAspectRatio;
    } else {
      targetHeight = containerRect.height;
      targetWidth = containerRect.height * cropAspectRatio;
    }
  } else {
    const { width: fittedWidth, height: fittedHeight } = calculateFittedCanvasDimensions(
      currentWidth,
      currentHeight,
      containerRect.width,
      containerRect.height
    );

    targetWidth = fittedWidth;
    targetHeight = fittedHeight;
  }

  if (!shouldResize(currentWidth, currentHeight, targetWidth, targetHeight)) {
    return;
  }

  const scaleX = targetWidth / currentWidth;
  const scaleY = targetHeight / currentHeight;

  canvas.setDimensions({ width: targetWidth, height: targetHeight });

  const backgroundImage = canvas.backgroundImage;
  if (backgroundImage) {
    backgroundImage.set({
      left: targetWidth / 2,
      top: targetHeight / 2,
      originX: "center",
      originY: "center",
      scaleX: (backgroundImage.scaleX || 1) * scaleX,
      scaleY: (backgroundImage.scaleY || 1) * scaleY,
    });
  }

  canvas.forEachObject((obj) => {
    const name = (obj as any).name;
    if (obj === backgroundImage) return;
    if (name === "magnifierLens") return;

    obj.set({
      left: (obj.left || 0) * scaleX,
      top: (obj.top || 0) * scaleY,
      scaleX: (obj.scaleX || 1) * scaleX,
      scaleY: (obj.scaleY || 1) * scaleY,
    });
    obj.setCoords();
  });

  scaleCanvasObjects(canvas, 1);

  syncMagnifierOnCanvasChange(canvas);

  if (cropData?.isCropped && cropData.normalizedCropRect) {
    applyCropFromNormalized(canvas, cropData.normalizedCropRect);
  }

  canvas.renderAll();
};

export const useResponsiveCanvas = ({
  fabricCanvas,
  containerRef,
  cropData,
}: UseResponsiveCanvasProps) => {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const resizeCanvas = useCallback(() => {
    if (!fabricCanvas?.current || !containerRef.current) return;

    handleCanvasResize(fabricCanvas.current, containerRef.current, cropData);
  }, [fabricCanvas, containerRef, cropData]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    resizeObserverRef.current = new ResizeObserver(() => {
      resizeCanvas();
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;
    };
  }, [containerRef, resizeCanvas]);

  return {
    resizeCanvas,
  };
};
