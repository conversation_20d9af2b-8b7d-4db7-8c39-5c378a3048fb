import { Canvas } from "fabric";
import { FilterParams } from "./FilterParams";
import { TransformState, BgTransformSnapshot } from "./TransformState";
import { CropData } from "./CropData";

export interface UseResponsiveCanvasProps {
  fabricCanvas: React.RefObject<Canvas>;
  containerRef: React.RefObject<HTMLElement | null>;
  cropData?: CropData;
}

export interface SetupCanvasParams {
  canvasElement: HTMLCanvasElement;
  imageUrl: string;
  annotations?: any;
  filters?: FilterParams;
  existingCanvas?: Canvas | null;
  transformState?: TransformState;
}

export interface LoadImageOptions {
  containerRect?: DOMRect;
}

export interface SavedAnnotationsSnapshot {
  objects?: any[];
  canvasWidth?: number;
  canvasHeight?: number;
  transformStateAtSnapshot?: TransformState;
  bgTransformAtSnapshot?: BgTransformSnapshot;
}
