import { Types } from "@cornerstonejs/core";

const volumeOriginalPoints = new Map<string, number[][]>();

export function adjustVolumeShift(viewport: Types.IVolumeViewport, shiftValue: number): void {
  const viewportId = viewport.id || "default";

  if (!volumeOriginalPoints.has(viewportId)) {
    const volumeActor = viewport.getDefaultActor().actor as Types.VolumeActor;
    const ofun = volumeActor.getProperty().getScalarOpacity(0);

    const opacityPointValues = [];
    const size = ofun.getSize();
    for (let pointIdx = 0; pointIdx < size; pointIdx++) {
      const opacityPointValue = [0, 0, 0, 0];
      ofun.getNodeValue(pointIdx, opacityPointValue);
      opacityPointValues.push([...opacityPointValue]);
    }
    volumeOriginalPoints.set(viewportId, opacityPointValues);
  }

  const originalPoints = volumeOriginalPoints.get(viewportId);
  if (!originalPoints || originalPoints.length === 0) return;

  const volumeActor = viewport.getDefaultActor().actor as Types.VolumeActor;
  const ofun = volumeActor.getProperty().getScalarOpacity(0);

  ofun.removeAllPoints();
  originalPoints.forEach((opacityPointValue) => {
    const shiftedPoint = [...opacityPointValue];
    shiftedPoint[0] += shiftValue;
    ofun.addPoint(shiftedPoint[0], shiftedPoint[1]);
  });

  viewport.render();
}

export const createVolumeShiftHandler = (
  viewportRef: React.RefObject<Types.IVolumeViewport | Types.IStackViewport | null>,
  setShift: (v: number) => void,
  is3D: boolean
) => {
  return (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!viewportRef.current || !is3D) return;
    setShift(value);
    adjustVolumeShift(viewportRef.current as Types.IVolumeViewport, value);
  };
};
