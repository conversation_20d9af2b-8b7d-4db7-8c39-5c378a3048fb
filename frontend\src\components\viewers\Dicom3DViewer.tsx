import { useRef, useEffect, useState } from "react";
import { Types, cache, RenderingEngine } from "@cornerstonejs/core";
import { ToolGroupManager } from "@cornerstonejs/tools";
import {
  initializeCornerstone,
  setup3dViewport,
  setup2dViewport,
  loadDicomVolume,
  loadDicomStack,
} from "@/lib/dicom/core";
import { saveVolumeConfig } from "@/services/api";
import type { VolumeViewerProps } from "@/models";
import { useVolumeResize } from "@/hooks";
import { adjustVolumeShift, createVolumeShiftHandler } from "@/lib/dicom/utils";
import { setupViewer, volume2dModeConfig, volumeViewerConfig } from "@/lib/dicom/config";

import {
  RENDER_ENGINE_ID_3D,
  VIEWPORT_ID_3D,
  TOOL_GROUP_ID_3D,
  DICOM_VOLUME_ID,
} from "@/lib/dicom/constants";

const Dicom3DViewer: React.FC<VolumeViewerProps> = ({ data }) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<Types.IStackViewport | Types.IVolumeViewport | null>(null);
  const dicomFilesRef = useRef<string[]>([]);

  const [isInitialized, setIsInitialized] = useState(false);
  const [is3D, setIs3D] = useState(true);
  const [shift, setShift] = useState(data.viewer.configs.shift);
  useVolumeResize(renderingEngineRef, VIEWPORT_ID_3D, isInitialized);

  const handleSave = async () => {
    await saveVolumeConfig(data.id, { shift });
  };

  const handleShiftChange = createVolumeShiftHandler(viewportRef, setShift, is3D);

  const switchTo3D = async () => {
    if (!elementRef.current || !renderingEngineRef.current) return;
    ToolGroupManager.destroyToolGroup(TOOL_GROUP_ID_3D);
    const viewport = setup3dViewport(
      renderingEngineRef.current,
      elementRef.current,
      VIEWPORT_ID_3D
    );
    viewportRef.current = viewport;
    setupViewer(TOOL_GROUP_ID_3D, VIEWPORT_ID_3D, RENDER_ENGINE_ID_3D, volumeViewerConfig);

    await loadDicomVolume(viewport, dicomFilesRef.current, DICOM_VOLUME_ID);
    adjustVolumeShift(viewport, shift);
    setIs3D(true);
  };
  const switchTo2D = async () => {
    if (!elementRef.current || !renderingEngineRef.current) return;
    ToolGroupManager.destroyToolGroup(TOOL_GROUP_ID_3D);
    const viewport = setup2dViewport(
      renderingEngineRef.current,
      elementRef.current,
      VIEWPORT_ID_3D
    );
    viewportRef.current = viewport;
    setupViewer(TOOL_GROUP_ID_3D, VIEWPORT_ID_3D, RENDER_ENGINE_ID_3D, volume2dModeConfig);
    await loadDicomStack(viewport, dicomFilesRef.current);
    setIs3D(false);
  };

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);

    return () => {
      ToolGroupManager.destroyToolGroup(TOOL_GROUP_ID_3D);
      renderingEngineRef.current?.destroy();
      cache.purgeCache();
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !elementRef.current) return;
    const initializeViewer = async () => {
      cache.purgeCache();
      const renderingEngine = new RenderingEngine(RENDER_ENGINE_ID_3D);
      renderingEngineRef.current = renderingEngine;
      const element = elementRef.current;
      if (!element) return;
      const viewport = setup3dViewport(renderingEngine, element, VIEWPORT_ID_3D);
      viewportRef.current = viewport;
      setupViewer(TOOL_GROUP_ID_3D, VIEWPORT_ID_3D, RENDER_ENGINE_ID_3D, volumeViewerConfig);
      dicomFilesRef.current = data.viewer.imageUrl;
      await loadDicomVolume(viewport, data.viewer.imageUrl, DICOM_VOLUME_ID);
      setIs3D(true);
      adjustVolumeShift(viewport, shift);
    };
    initializeViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, data.viewer.imageUrl]);

  return (
    <div className="dicom-3d-viewer">
      <div ref={elementRef} style={{ width: "100%", height: "100%" }} />

      {is3D && (
        <div className="controls">
          <div className="control-group">
            <label>Shift:</label>
            <input
              type="range"
              min="0"
              max="3000"
              step="100"
              value={shift}
              onChange={handleShiftChange}
            />
            <span>{shift}</span>
          </div>

          <button className="save-button" onClick={handleSave}>
            Save Settings
          </button>
        </div>
      )}

      <div className="mode-switcher">
        <span className={`mode-switcher-item ${is3D ? "active" : "inactive"}`} onClick={switchTo3D}>
          3D
        </span>
        <span className="mode-switcher-separator">|</span>
        <span
          className={`mode-switcher-item ${!is3D ? "active" : "inactive"}`}
          onClick={switchTo2D}
        >
          2D
        </span>
      </div>
    </div>
  );
};

export default Dicom3DViewer;
