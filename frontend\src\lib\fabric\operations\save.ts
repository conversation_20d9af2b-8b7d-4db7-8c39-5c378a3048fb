import { Canvas } from "fabric";
import { saveFabricConfig } from "@/services/api";
import type { CropData, FabricConfig, TransformState, CalibrationData } from "@/models";
import { getCalibrationFromLocalStorage, clearCalibrationFromLocalStorage } from "./index";

/*
Creates a save function that serializes annotations, filters, crop, transforms,
and calibration data and persists them through the API.
*/
export const createSaveHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  dataId: string,
  filters: Record<string, any>,
  canvasCropData: CropData,
  transformState: TransformState,
  calibrationData?: CalibrationData
) => {
  return async () => {
    if (fabricCanvas?.current) {
      // Ensure all objects have proper toObject overrides to save selection properties
      fabricCanvas.current.forEachObject((obj: any) => {
        if (!obj._hasCustomToObject && obj.name !== "backgroundImage") {
          const originalToObject = obj.toObject;
          obj.toObject = function (propertiesToInclude?: any[]) {
            const extraGlobal = ["perPixelTargetFind"];
            // protractor-specific custom props to persist
            const protractorProps = [
              "id",
              "parentId",
              "handleOf",
              "rayBId",
              "textId",
              "handleAId",
              "handleBId",
              "vertexHandleId",
              "rotateHandleId",
              "vertex",
              "phase",
              "arcRadius",
              "handleRadius",
              "__selfAdded",
              "name",
              "customType",
              "excludeFromActiveGroup",
              "selectable",
              "evented",
              "hasControls",
              "hasBorders",
              "hoverCursor",
              "moveCursor",
            ];

            const selectionProps = Array.from(
              new Set([
                ...(propertiesToInclude ?? []),
                ...extraGlobal,
                ...protractorProps,
              ])
            );

            return originalToObject.call(this, selectionProps);
          };
          obj._hasCustomToObject = true;
        }
      });

      const annotations = fabricCanvas.current.toJSON();
      annotations.canvasWidth = fabricCanvas.current.getWidth();
      annotations.canvasHeight = fabricCanvas.current.getHeight();

      const finalCalibrationData = getCalibrationFromLocalStorage();

      const fabricConfig: FabricConfig = {
        brightness: filters.brightness,
        contrast: filters.contrast,
        grayscale: filters.grayscale,
        invert: filters.invert,
        sharpness: filters.sharpness,
        gammaR: filters.gammaR,
        gammaG: filters.gammaG,
        gammaB: filters.gammaB,
        annotations,
        cropData: canvasCropData,
        transformState,
        ...(finalCalibrationData && { calibrationData: finalCalibrationData }),
      };

      await saveFabricConfig(dataId, fabricConfig);
      if (finalCalibrationData && !calibrationData) {
        clearCalibrationFromLocalStorage();
      }
    }
  };
};
