import type { ToolMode } from "@/models";
import { Canvas } from "fabric";
const inheritedFont = window.getComputedStyle(document.body).fontFamily;

/*
Gets configuration for specific object names (like protractor components)
*/
export const getObjectConfig = (objectName: string) => {
  switch (objectName) {
    case "protractorHandle":
      return {
        originX: "center" as const,
        originY: "center" as const,
        opacity: 1,
        fill: "#ffffff",
        stroke: "#000000",
        strokeWidth: 1,
        radius: 3,
        perPixelTargetFind: false,
        selectable: false,
        evented: false,
        hasBorders: false,
        hasControls: false,
        hoverCursor: "crosshair",
        moveCursor: "crosshair",
        objectCaching: true,
        padding: 0,
      };
    case "protractorRotateHandle":
      return {
        originX: "center" as const,
        originY: "center" as const,
        opacity: 1,
        fill: "limegreen",
        stroke: "#000000",
        strokeWidth: 1,
        radius: 3,
        perPixelTargetFind: false,
        selectable: false,
        evented: false,
        hasBorders: false,
        hasControls: true,
        hoverCursor: "grab",
        moveCursor: "grab",
        objectCaching: true,
        padding: 0,
        lockScalingX: true,
        lockScalingY: true,
        lockUniScaling: true,
      };
    default:
      return {};
  }
};

export const getToolConfig = (mode: ToolMode) => {
  switch (mode) {
    case "freehand":
      return {
        strokeWidth: 2,
        strokeUniform: true,
        perPixelTargetFind: true,
      };
    case "text":
      return {
        fontSize: 20,
        fontFamily: inheritedFont,
        perPixelTargetFind: false,
      };
    case "rect":
      return {
        width: 1,
        height: 1,
        fill: "transparent",
        strokeWidth: 2,
        strokeUniform: true,
        perPixelTargetFind: true,
      };
    case "highlight":
      return {
        width: 1,
        height: 1,
        fill: "rgba(255, 255, 0, 0.25)",
        stroke: "#e1c542",
        strokeWidth: 0,
        strokeUniform: true,
        name: "highlight",
        perPixelTargetFind: true,
      };
    case "circle":
      return {
        radius: 1,
        fill: "transparent",
        strokeWidth: 2,
        strokeUniform: true,
        perPixelTargetFind: true,
      };
    case "line":
      return {
        strokeWidth: 2,
        strokeUniform: true,
        name: "line",
        perPixelTargetFind: true,
      };
    case "crop":
      return {
        width: 1,
        height: 1,
        fill: "rgba(255, 255, 255, .1)",
        stroke: "#ff0000",
        strokeWidth: 1,
        strokeDashArray: [5, 5],
        strokeUniform: true,
        name: "cropRect",
      };
    case "measure":
      return {
        strokeWidth: 2,
        name: "measurementLine",
        strokeUniform: true,
        perPixelTargetFind: true,
        hoverCursor: "move",
        objectCaching: false,
      };
    case "calibrate":
      return {
        width: 4,
        height: 4,
        fill: "red",
        stroke: "white",
        strokeWidth: 1,
        originX: "center",
        originY: "center",
        name: "calibrateLine",
      };
    case "arrow":
      return {
        strokeWidth: 2,
        strokeUniform: true,
        name: "arrow",
        arrowHeadSize: 14,
        perPixelTargetFind: true,
        hoverCursor: "move",
        objectCaching: false,
      };
    case "protractor":
      return {
        stroke: "#ff0000",
        strokeWidth: 3,
        hasControls: false,
        hasBorders: false,
        strokeUniform: true,
        name: "protractor",
        perPixelTargetFind: false,
        hoverCursor: "move",
        objectCaching: true,
        strokeLineCap: "butt",
        strokeLineJoin: "miter",
        arcRadius: 32,
        handleRadius: 3,
        selectable: false,
        evented: false,
      };
    default:
      return {};
  }
};

export const constrainToCanvas = (pointer: { x: number; y: number }, canvas: Canvas) => {
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  return {
    x: Math.max(0, Math.min(pointer.x, canvasWidth - 1)),
    y: Math.max(0, Math.min(pointer.y, canvasHeight - 1)),
  };
};

export const transformPointer = (
  pointer: { x: number; y: number },
  canvas: Canvas
): { x: number; y: number } => {
  const vpt = canvas.viewportTransform;
  const needsTransform = vpt && (vpt[0] !== 1 || vpt[3] !== 1 || vpt[4] !== 0 || vpt[5] !== 0);

  let transformedPointer = pointer;

  if (needsTransform) {
    transformedPointer = {
      x: (pointer.x - vpt[4]) / vpt[0],
      y: (pointer.y - vpt[5]) / vpt[3],
    };
  }

  return transformedPointer;
};
