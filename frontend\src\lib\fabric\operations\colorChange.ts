import { Canvas } from "fabric";
import { isArrow, isMeasurementLine, updateProtractorColor } from "./index";

/*
Updates arrow head color by triggering canvas re-render.
Arrow head color is automatically updated via custom render method using line stroke.
*/
const updateArrowHeadColor = (arrowLine: any, canvas?: any): void => {
  if (canvas) {
    canvas.requestRenderAll();
  } else if (arrowLine.canvas) {
    arrowLine.canvas.requestRenderAll();
  }
};

/*
Updates measurement text color by triggering canvas re-render.
Text color is automatically updated via custom render method using line stroke.
*/
const updateMeasurementTextColor = (measurementLine: any, canvas?: any): void => {
  if (canvas) {
    canvas.requestRenderAll();
  } else if (measurementLine.canvas) {
    measurementLine.canvas.requestRenderAll();
  }
};

/*
Updates the color of a single annotation object based on its type.
Handles different color properties (stroke vs fill) and associated objects:
- Text: uses fill color
- Shapes: uses stroke color
- Highlights: uses both fill (with opacity) and stroke
- Protractors: uses stroke color (basic color change only)
- Arrows: updates line stroke and triggers arrow head re-render
- Measurement lines: updates line stroke and triggers text re-render
*/
export const applyColorToSingleObject = (obj: any, newColor: string, canvas?: any): void => {
  if (!obj || !newColor) return;

  const objName = obj.name;
  const objType = obj.type;

  switch (objType) {
    case "textbox":
      obj.set({ fill: newColor });
      break;
    case "rect":
    case "circle":
    case "line":
      if (objName === "highlight") {
        return;
      } else if (objName === "protractor") {
        updateProtractorColor(canvas, obj, newColor);
      } else if (isArrow(obj)) {
        obj.set({ stroke: newColor });
        updateArrowHeadColor(obj, canvas);
      } else if (isMeasurementLine(obj)) {
        obj.set({ stroke: newColor });
        updateMeasurementTextColor(obj, canvas);
      } else {
        obj.set({ stroke: newColor });
      }
      break;

    case "path":
      obj.set({ stroke: newColor });
      break;

    default:
      if (objName === "protractor") {
        updateProtractorColor(canvas, obj, newColor);
      } else if (isArrow(obj)) {
        obj.set({ stroke: newColor });
        updateArrowHeadColor(obj, canvas);
      } else if (isMeasurementLine(obj)) {
        obj.set({ stroke: newColor });
        updateMeasurementTextColor(obj, canvas);
      } else {
        obj.set({ stroke: newColor });
      }
      break;
  }

  obj.setCoords();
};

/*
Updates the color of the currently selected object(s) on the canvas.
Handles both single selections and multiple selections (ActiveSelection).
Preserves the selection state after color changes by restoring it after rendering.
*/
export const changeSelectedAnnotationColors = (canvas: Canvas, newColor: string): void => {
  const activeObject = canvas.getActiveObject();

  if (!activeObject) return;

  const selectedObjects =
    activeObject.type === "activeselection" ? (activeObject as any).getObjects() : [activeObject];

  selectedObjects.forEach((obj: any) => {
    applyColorToSingleObject(obj, newColor, canvas);
  });

  const active = canvas.getActiveObject();
  if (active) {
    if (active.type === "activeselection") {
      canvas.setActiveObject(active);
    } else {
      canvas.setActiveObject(selectedObjects[0]);
    }
  }
  canvas.requestRenderAll();
};

/*
Gets the current color from a selected object.
Returns the appropriate color property based on object type.
*/
export const getObjectColor = (obj: any): string | undefined => {
  if (!obj) return undefined;

  // For text objects, use fill color
  if (obj.type === "textbox" && obj.fill) {
    return obj.fill;
  }

  // For other objects, use stroke color
  if (obj.stroke) {
    return obj.stroke;
  }

  return undefined;
};

/*
Context-aware color update based on selection state.
Selected object: changes only that object's color.
No selection: calls onDefaultChange callback for session storage update.
*/
export const applyColorContextAware = (
  canvas: Canvas,
  color: string,
  onDefaultChange?: (color: string) => void
): void => {
  const selectedObject = canvas.getActiveObject();

  if (selectedObject) {
    changeSelectedAnnotationColors(canvas, color);
  } else {
    onDefaultChange?.(color);
  }
};
