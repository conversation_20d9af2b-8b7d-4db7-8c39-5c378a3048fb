/*
Utility functions for managing annotation defaults in session storage.
Provides centralized access to default values for all annotation properties.
*/

// Session storage keys
export const SESSION_STORAGE_KEYS = {
  ANNOTATION_COLOR: "annotationColor",
  STROKE_WIDTH: "strokeWidth",
  FONT_SIZE: "fontSize",
  IS_BOLD: "isBold",
  IS_ITALIC: "isItalic",
  IS_UNDERLINE: "isUnderline",
  FONT_FAMILY: "fontFamily",
  MAGNIFIER_ZOOM: "magnifierZoom",
  MAGNIFIER_RADIUS: "magnifierRadius",
  LINE_STYLE: "lineStyle",
} as const;

// Default values
export const DEFAULT_VALUES = {
  ANNOTATION_COLOR: "#ff0000",
  STROKE_WIDTH: 2,
  FONT_SIZE: 20,
  IS_BOLD: false,
  IS_ITALIC: false,
  IS_UNDERLINE: false,
  FONT_FAMILY: "Arial",
  MAG<PERSON><PERSON>ER_ZOOM: 2,
  MAGNIFIER_RADIUS: 100,
  LIN<PERSON>_STYLE: "solid",
} as const;

/*
Gets annotation color from session storage or returns default.
*/
export const getAnnotationColorDefault = (): string => {
  return (
    sessionStorage.getItem(SESSION_STORAGE_KEYS.ANNOTATION_COLOR) || DEFAULT_VALUES.ANNOTATION_COLOR
  );
};

/*
Sets annotation color in session storage.
*/
export const setAnnotationColorDefault = (color: string): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.ANNOTATION_COLOR, color);
};

/*
Gets stroke width from session storage or returns default.
*/
export const getStrokeWidthDefault = (): number => {
  return parseInt(
    sessionStorage.getItem(SESSION_STORAGE_KEYS.STROKE_WIDTH) ||
      DEFAULT_VALUES.STROKE_WIDTH.toString()
  );
};

/*
Sets stroke width in session storage.
*/
export const setStrokeWidthDefault = (width: number): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.STROKE_WIDTH, width.toString());
};

/*
Gets font size from session storage or returns default.
*/
export const getFontSizeDefault = (): number => {
  return parseInt(
    sessionStorage.getItem(SESSION_STORAGE_KEYS.FONT_SIZE) || DEFAULT_VALUES.FONT_SIZE.toString()
  );
};

/*
Sets font size in session storage.
*/
export const setFontSizeDefault = (size: number): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.FONT_SIZE, size.toString());
};

/*
Gets bold state from session storage or returns default.
*/
export const getBoldDefault = (): boolean => {
  return sessionStorage.getItem(SESSION_STORAGE_KEYS.IS_BOLD) === "true";
};

/*
Sets bold state in session storage.
*/
export const setBoldDefault = (isBold: boolean): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.IS_BOLD, isBold.toString());
};

/*
Gets italic state from session storage or returns default.
*/
export const getItalicDefault = (): boolean => {
  return sessionStorage.getItem(SESSION_STORAGE_KEYS.IS_ITALIC) === "true";
};

/*
Sets italic state in session storage.
*/
export const setItalicDefault = (isItalic: boolean): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.IS_ITALIC, isItalic.toString());
};

/*
Gets font family from session storage or returns default.
*/
export const getFontFamilyDefault = (): string => {
  return sessionStorage.getItem(SESSION_STORAGE_KEYS.FONT_FAMILY) || DEFAULT_VALUES.FONT_FAMILY;
};

/*
Sets font family in session storage.
*/
export const setFontFamilyDefault = (fontFamily: string): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.FONT_FAMILY, fontFamily);
};

/*
Gets magnifier zoom from session storage or returns default.
*/
export const getMagnifierZoomDefault = (): number => {
  return parseInt(
    sessionStorage.getItem(SESSION_STORAGE_KEYS.MAGNIFIER_ZOOM) ||
      DEFAULT_VALUES.MAGNIFIER_ZOOM.toString()
  );
};

/*
Sets magnifier zoom in session storage.
*/
export const setMagnifierZoomDefault = (zoom: number): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.MAGNIFIER_ZOOM, zoom.toString());
};

/*
Gets magnifier radius from session storage or returns default.
*/
export const getMagnifierRadiusDefault = (): number => {
  return parseInt(
    sessionStorage.getItem(SESSION_STORAGE_KEYS.MAGNIFIER_RADIUS) ||
      DEFAULT_VALUES.MAGNIFIER_RADIUS.toString()
  );
};

/*
Sets magnifier radius in session storage.
*/
export const setMagnifierRadiusDefault = (radius: number): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.MAGNIFIER_RADIUS, radius.toString());
};

/*
Gets line style from session storage or returns default.
*/
export const getLineStyleDefault = (): string => {
  return sessionStorage.getItem(SESSION_STORAGE_KEYS.LINE_STYLE) || DEFAULT_VALUES.LINE_STYLE;
};

/*
Sets line style in session storage.
*/
export const setLineStyleDefault = (lineStyle: string): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.LINE_STYLE, lineStyle);
};

/*
Gets underline state from session storage or returns default.
*/
export const getUnderlineDefault = (): boolean => {
  return sessionStorage.getItem(SESSION_STORAGE_KEYS.IS_UNDERLINE) === "true";
};

/*
Sets underline state in session storage.
*/
export const setUnderlineDefault = (isUnderline: boolean): void => {
  sessionStorage.setItem(SESSION_STORAGE_KEYS.IS_UNDERLINE, isUnderline.toString());
};
/*
Gets all default values from session storage.
*/
export const getAllDefaults = () => ({
  annotationColor: getAnnotationColorDefault(),
  strokeWidth: getStrokeWidthDefault(),
  fontSize: getFontSizeDefault(),
  isBold: getBoldDefault(),
  isItalic: getItalicDefault(),
  isUnderline: getUnderlineDefault(),
  fontFamily: getFontFamilyDefault(),
  magnifierZoom: getMagnifierZoomDefault(),
  magnifierRadius: getMagnifierRadiusDefault(),
  lineStyle: getLineStyleDefault(),
});
