import { Canvas, FabricImage } from "fabric";
import {
  scaleCanvasObjects,
  restoreMeasurementRender,
  restoreArrowRender,
  restoreTextRender,
} from "./index";

/*
Loads annotations from JSON data and restores custom rendering methods.
Scales objects proportionally if canvas dimensions changed since save.
*/
export const loadAnnotations = async (canvas: Canvas, annotations: any): Promise<void> => {
  if (!annotations || !canvas) return;
  const backgroundImage = canvas.backgroundImage as FabricImage | null;
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const annotationsData = annotations as {
    canvasWidth?: number;
    canvasHeight?: number;
    objects?: any[];
    [key: string]: any;
  };

  await canvas.loadFromJSON(annotations);

  if (backgroundImage) {
    canvas.backgroundImage = backgroundImage;
  }

  // Scale annotations if canvas dimensions changed since save
  if (annotationsData.canvasWidth && annotationsData.canvasHeight) {
    const scaleX = currentWidth / annotationsData.canvasWidth;
    const scaleY = currentHeight / annotationsData.canvasHeight;
    scaleCanvasObjects(canvas, Math.min(scaleX, scaleY));
  }

  // Restore custom render methods for measurements
  canvas
    .getObjects()
    .filter((obj: any) => obj.name === "measurementLine" || obj.customType === "measurementLine")
    .forEach((line: any) => {
      line.name = "measurementLine";
      line.customType = "measurementLine";
      line.set({ objectCaching: false });
      restoreMeasurementRender(line);
    });

  // Restore custom render methods for arrows
  canvas
    .getObjects()
    .filter((obj: any) => obj.name === "arrow" || obj.customType === "arrow")
    .forEach((line: any) => {
      line.name = "arrow";
      line.customType = "arrow";
      restoreArrowRender(line);
    });

  // Restore text control properties
  canvas
    .getObjects()
    .filter((obj: any) => obj.type === "textbox")
    .forEach((textObj: any) => restoreTextRender(textObj));

  // Remove orphaned measurement text objects
  canvas
    .getObjects()
    .filter(
      (obj: any) =>
        obj.name === "measurementText" || (obj.type === "text" && obj.text?.includes("mm"))
    )
    .forEach((textObj) => canvas.remove(textObj));

  canvas.forEachObject((obj) => {
    if ((obj as any)?.name !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  canvas.calcOffset();
  canvas.renderAll();
};
