import { Canvas } from "fabric";

/*
Updates font weight (bold) for selected text objects on the canvas.
Only affects textbox objects, ignores other shape types.
*/
export const changeSelectedObjectsFontWeight = (canvas: Canvas, isBold: boolean): void => {
  const activeObject = canvas.getActiveObject();

  if (!activeObject) return;

  const selectedObjects =
    activeObject.type === "activeselection" ? (activeObject as any).getObjects() : [activeObject];

  selectedObjects.forEach((obj: any) => {
    // Only apply to text objects
    if (obj.type === "textbox") {
      obj.set({ fontWeight: isBold ? "bold" : "normal" });
    }
  });

  canvas.renderAll();
};

/*
Updates font style (italic) for selected text objects on the canvas.
Only affects textbox objects, ignores other shape types.
*/
export const changeSelectedObjectsFontStyle = (canvas: Canvas, isItalic: boolean): void => {
  const activeObject = canvas.getActiveObject();

  if (!activeObject) return;

  const selectedObjects =
    activeObject.type === "activeselection" ? (activeObject as any).getObjects() : [activeObject];

  selectedObjects.forEach((obj: any) => {
    // Only apply to text objects
    if (obj.type === "textbox") {
      obj.set({ fontStyle: isItalic ? "italic" : "normal" });
    }
  });

  canvas.renderAll();
};

/*
Gets the current font weight from a selected text object.
Returns undefined if object is not a text object.
*/
export const getObjectFontWeight = (obj: any): boolean | undefined => {
  if (!obj || obj.type !== "textbox") return undefined;

  return obj.fontWeight === "bold";
};

/*
Gets the current font style from a selected text object.
Returns undefined if object is not a text object.
*/
export const getObjectFontStyle = (obj: any): boolean | undefined => {
  if (!obj || obj.type !== "textbox") return undefined;

  return obj.fontStyle === "italic";
};

/*
Updates the underline state of selected text objects.
Only applies to textbox objects.
*/
export const changeSelectedObjectsUnderline = (canvas: Canvas, isUnderline: boolean): void => {
  const selectedObject = canvas.getActiveObject();
  if (!selectedObject) return;

  const selectedObjects =
    selectedObject.type === "activeselection"
      ? (selectedObject as any).getObjects()
      : [selectedObject];

  selectedObjects.forEach((obj: any) => {
    if (obj.type === "textbox") {
      obj.set({ underline: isUnderline });
    }
  });

  canvas.renderAll();
};

/*
Gets the current underline state from a selected text object.
Returns undefined if object is not a text object.
*/
export const getObjectUnderline = (obj: any): boolean | undefined => {
  if (!obj || obj.type !== "textbox") return undefined;

  return obj.underline === true;
};

/*
Context-aware font weight (bold) update based on selection state.
Selected object: changes only that object's font weight.
No selection: calls onDefaultChange callback for session storage update.
*/
export const applyFontWeightContextAware = (
  canvas: Canvas,
  isBold: boolean,
  onDefaultChange?: () => void
): void => {
  const selectedObject = canvas.getActiveObject();

  if (selectedObject) {
    changeSelectedObjectsFontWeight(canvas, isBold);
  } else {
    onDefaultChange?.();
  }
};

/*
Context-aware font style (italic) update based on selection state.
Selected object: changes only that object's font style.
No selection: calls onDefaultChange callback for session storage update.
*/
export const applyFontStyleContextAware = (
  canvas: Canvas,
  isItalic: boolean,
  onDefaultChange?: () => void
): void => {
  const selectedObject = canvas.getActiveObject();

  if (selectedObject) {
    changeSelectedObjectsFontStyle(canvas, isItalic);
  } else {
    onDefaultChange?.();
  }
};

/*
Context-aware underline update based on selection state.
Selected object: changes only that object's underline state.
No selection: calls onDefaultChange callback for session storage update.
*/
export const applyUnderlineContextAware = (
  canvas: Canvas,
  isUnderline: boolean,
  onDefaultChange?: (isUnderline: boolean) => void
): void => {
  const selectedObject = canvas.getActiveObject();

  if (selectedObject) {
    changeSelectedObjectsUnderline(canvas, isUnderline);
  } else {
    onDefaultChange?.(isUnderline);
  }
};
