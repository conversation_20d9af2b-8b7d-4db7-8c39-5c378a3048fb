import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import App from "@/App.tsx";
import "@/styles.scss";
import "@blueprintjs/core/lib/css/blueprint.css";
import "@blueprintjs/icons/lib/css/blueprint-icons.css";
import { OverlaysProvider } from "@blueprintjs/core";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <OverlaysProvider>
      <App />
    </OverlaysProvider>
  </StrictMode>
);
