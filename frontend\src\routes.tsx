import { apiGetFileList } from "@/services/api";
import RootLayout from "@/layouts/RootLayout";
import Home from "@/pages/Home";
import PatientFileViewer from "@/pages/PatientFileViewer";

export const routes = [
  {
    path: "/",
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: "patient/:patientId/fileviewer",
        loader: async ({ params }: { params: Record<string, string | undefined> }) => {
          const { patientId } = params as { patientId: string };
          return await apiGetFileList(patientId);
        },
        element: <PatientFileViewer />,
      },
    ],
  },
];
