import { useEffect, useState, useRef } from "react";
import { PencilBrush, FabricObject } from "fabric";
import type { ToolMode, FabricMeasurementLine, UseFabricToolsProps } from "@/models";
import {
  setToolMode,
  getToolConfig,
  startDrawing,
  updateDrawing,
  completeDrawing,
  transformPointer,
} from "@/lib/fabric/tools";
import {
  advanceProtractorPhase,
  updateProtractorSize,
  isProtractor,
} from "@/lib/fabric/operations";
import { setupCanvasEventListeners } from "@/lib/fabric/events/canvasEventListeners";
import type { UndoTrackingState, FabricObjectState } from "@/models";
import { createMeasurementCheckHandler } from "@/lib/fabric/operations";

/*
Handles protractor-specific mouse down logic
*/
const handleProtractorMouseDown = (
  protractor: any,
  currentPoint: { x: number; y: number },
  canvas: any,
  startPointRef: React.MutableRefObject<{ x: number; y: number } | null>,
  currentShapeRef: React.MutableRefObject<FabricObject | FabricMeasurementLine | null>,
  onCrop: (() => void) | undefined,
  onShapeCreated: (() => void) | undefined,
  calibrationData: any
): boolean => {
  if (protractor.phase === 1) {
    updateProtractorSize(protractor, currentPoint, canvas);
    advanceProtractorPhase(protractor);
    canvas.setActiveObject(protractor);
    startPointRef.current = protractor.vertex;
    canvas.requestRenderAll();
    return true;
  }

  if (protractor.phase === 2) {
    updateProtractorSize(protractor, currentPoint, canvas);
    const done = advanceProtractorPhase(protractor);
    if (!done) {
      canvas.setActiveObject(protractor);
      canvas.requestRenderAll();
      return true;
    }

    completeDrawing(
      "protractor" as any,
      protractor,
      canvas,
      onCrop,
      onShapeCreated,
      calibrationData
    );

    startPointRef.current = null;
    currentShapeRef.current = null;
    canvas.discardActiveObject();
    canvas.requestRenderAll();
    return true;
  }

  return false;
};

/*
Handles calibration completion logic
*/
const handleCalibrationCompletion = (
  activeMode: ToolMode,
  canvas: any,
  showCalibrationModal: (() => void) | undefined
): void => {
  if (activeMode === "calibrate") {
    const count = canvas
      .getObjects("rect")
      .filter((obj: any) => obj.name === "calibrateLine").length;
    if (count === 2) {
      showCalibrationModal?.();
    }
  }
};

/*
Handles tool mode, mouse drawing interactions, and calibration gating for measure.
Connects Fabric events to drawingInteractions utilities.
*/
export const useFabricTools = ({
  fabricCanvas,
  cropData,
  onShapeCreated,
  onCrop,
  disableUndoTracking,
  enableUndoTracking,
  showCalibrationModal,
  calibrationData,
  onCalibrationPrompt,
}: UseFabricToolsProps) => {
  const [activeMode, setActiveMode] = useState<ToolMode | null>(null);
  const startPointRef = useRef<{ x: number; y: number } | null>(null);
  const currentShapeRef = useRef<FabricObject | FabricMeasurementLine | null>(null);

  // Switches active tool; for 'measure', requires calibration before enabling
  const changeToolMode = (mode: ToolMode) => {
    if (!fabricCanvas?.current) return;

    if (activeMode === mode) return;

    if (mode === "measure") {
      const checkCalibration = createMeasurementCheckHandler(calibrationData, onCalibrationPrompt);
      if (!checkCalibration()) {
        return;
      }
    }

    setActiveMode(mode);
    setToolMode(mode, fabricCanvas.current);
  };

  useEffect(() => {
    if (!fabricCanvas?.current) return;

    const canvas = fabricCanvas.current;

    // Configure freehand brush from tool config and annotation settings
    const config = getToolConfig("freehand") as any;
    canvas.freeDrawingBrush = new PencilBrush(canvas);
    const brushColor = (canvas as any).annotationColor || config.color;
    canvas.freeDrawingBrush.color = brushColor;

    // Use strokeWidth from annotation settings if available, otherwise use config default
    const annotationSettings = (canvas as any).annotationSettings;
    const brushWidth = annotationSettings?.strokeWidth || config.strokeWidth || 2;
    canvas.freeDrawingBrush.width = brushWidth;

    if (activeMode) {
      setToolMode(activeMode, canvas);
    }

    // Reuse undo/object-state structures if the canvas already has them
    const undoTracking: UndoTrackingState = (canvas as any).undoTrackingState || {
      isUndoingRef: { current: false },
      addUndoAction: () => {},
    };
    const objectStates: React.MutableRefObject<Map<string, FabricObjectState>> = (canvas as any)
      .objectStates || { current: new Map() };

    // Wire Fabric events to undo tracking and measurement/arrow modifiers
    const disposers = setupCanvasEventListeners(
      canvas,
      undoTracking,
      objectStates,
      calibrationData,
      onShapeCreated
    );

    const mouseDownHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!activeMode || !e.pointer) return;
      const canvas = fabricCanvas.current!;
      const currentPoint = transformPointer(e.pointer, canvas);

      // Handle protractor-specific logic
      if (
        activeMode === "protractor" &&
        currentShapeRef.current &&
        isProtractor(currentShapeRef.current)
      ) {
        const handled = handleProtractorMouseDown(
          currentShapeRef.current,
          currentPoint,
          canvas,
          startPointRef,
          currentShapeRef,
          onCrop,
          onShapeCreated,
          calibrationData
        );
        if (handled) return;
      }

      // Handle general drawing logic
      const result = startDrawing(
        activeMode,
        e.pointer,
        canvas,
        cropData?.isCropped || false,
        onShapeCreated
      );

      if (result) {
        startPointRef.current = result.startPoint;
        currentShapeRef.current = result.shape;
      }
    };

    const mouseMoveHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!currentShapeRef.current || !startPointRef.current || !e.pointer || !activeMode) return;

      // Update geometry during drag; measurement lines refresh overlay text
      updateDrawing(
        e.pointer,
        activeMode,
        canvas,
        currentShapeRef.current,
        startPointRef.current,
        calibrationData
      );
    };

    const mouseUpHandler = () => {
      if (!currentShapeRef.current || !activeMode) {
        startPointRef.current = null;
        currentShapeRef.current = null;
        return;
      }

      // Skip finish for protractor (handled in mouse down)
      if (activeMode === "protractor" && isProtractor(currentShapeRef.current)) {
        return;
      }

      // Complete drawing for other tools
      completeDrawing(
        activeMode,
        currentShapeRef.current,
        canvas,
        onCrop,
        onShapeCreated,
        calibrationData
      );

      // Handle calibration completion
      handleCalibrationCompletion(activeMode, canvas, showCalibrationModal);

      startPointRef.current = null;
      currentShapeRef.current = null;
    };

    const disposeMouseDown = canvas.on("mouse:down", mouseDownHandler);
    const disposeMouseMove = canvas.on("mouse:move", mouseMoveHandler);
    const disposeMouseUp = canvas.on("mouse:up", mouseUpHandler);

    return () => {
      disposeMouseDown();
      disposeMouseMove();
      disposeMouseUp();
      disposers.forEach((dispose) => dispose && dispose());
    };
  }, [
    fabricCanvas,
    activeMode,
    onShapeCreated,
    cropData,
    onCrop,
    disableUndoTracking,
    enableUndoTracking,
    calibrationData,
    showCalibrationModal,
  ]);

  return {
    activeMode,
    changeToolMode,
  };
};
