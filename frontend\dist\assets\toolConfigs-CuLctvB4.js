const o=window.getComputedStyle(document.body).fontFamily,s=r=>{switch(r){case"protractorHandle":return{originX:"center",originY:"center",opacity:1,fill:"#ffffff",stroke:"#000000",strokeWidth:1,radius:3,perPixelTargetFind:!1,selectable:!1,evented:!1,hasBorders:!1,hasControls:!1,hoverCursor:"crosshair",moveCursor:"crosshair",objectCaching:!0,padding:0};case"protractorRotateHandle":return{originX:"center",originY:"center",opacity:1,fill:"limegreen",stroke:"#000000",strokeWidth:1,radius:3,perPixelTargetFind:!1,selectable:!1,evented:!1,hasBorders:!1,hasControls:!0,hoverCursor:"grab",moveCursor:"grab",objectCaching:!0,padding:0,lockScalingX:!0,lockScalingY:!0,lockUniScaling:!0};default:return{}}},n=r=>{switch(r){case"freehand":return{strokeWidth:2,strokeUniform:!0,perPixelTargetFind:!0};case"text":return{fontSize:20,fontFamily:o,perPixelTargetFind:!1};case"rect":return{width:1,height:1,fill:"transparent",strokeWidth:2,strokeUniform:!0,perPixelTargetFind:!0};case"highlight":return{width:1,height:1,fill:"rgba(255, 255, 0, 0.25)",stroke:"#e1c542",strokeWidth:0,strokeUniform:!0,name:"highlight",perPixelTargetFind:!0};case"circle":return{radius:1,fill:"transparent",strokeWidth:2,strokeUniform:!0,perPixelTargetFind:!0};case"line":return{strokeWidth:2,strokeUniform:!0,name:"line",perPixelTargetFind:!0};case"crop":return{width:1,height:1,fill:"rgba(255, 255, 255, .1)",stroke:"#ff0000",strokeWidth:1,strokeDashArray:[5,5],strokeUniform:!0,name:"cropRect"};case"measure":return{strokeWidth:2,name:"measurementLine",strokeUniform:!0,perPixelTargetFind:!0,hoverCursor:"move",objectCaching:!1};case"calibrate":return{width:4,height:4,fill:"red",stroke:"white",strokeWidth:1,originX:"center",originY:"center",name:"calibrateLine"};case"arrow":return{strokeWidth:2,strokeUniform:!0,name:"arrow",arrowHeadSize:14,perPixelTargetFind:!0,hoverCursor:"move",objectCaching:!1};case"protractor":return{stroke:"#ff0000",strokeWidth:3,hasControls:!1,hasBorders:!1,strokeUniform:!0,name:"protractor",perPixelTargetFind:!1,hoverCursor:"move",objectCaching:!0,strokeLineCap:"butt",strokeLineJoin:"miter",arcRadius:32,handleRadius:3,selectable:!1,evented:!1};default:return{}}},l=(r,t)=>{const e=t.getWidth(),a=t.getHeight();return{x:Math.max(0,Math.min(r.x,e-1)),y:Math.max(0,Math.min(r.y,a-1))}},c=(r,t)=>{const e=t.viewportTransform,a=e&&(e[0]!==1||e[3]!==1||e[4]!==0||e[5]!==0);let i=r;return a&&(i={x:(r.x-e[4])/e[0],y:(r.y-e[5])/e[3]}),i};export{n as a,l as c,s as g,c as t};
