import React from "react";
import { <PERSON><PERSON>, ControlGroup, Icon, <PERSON>lider, Tag } from "@blueprintjs/core";
import type { GammaControlsProps } from "@/models";

const GammaControls: React.FC<GammaControlsProps> = ({
  gammaR,
  gammaG,
  gammaB,
  disableGamma,
  onGammaRChange,
  onGammaGChange,
  onGammaBChange,
}) => {
  const items = [
    {
      icon: "full-circle",
      label: "Gamma R",
      value: gammaR,
      onChange: onGammaRChange,
      min: 1,
      max: 2.2,
      step: 0.05,
      def: 1,
      intent: "danger" as const,
    },
    {
      icon: "full-circle",
      label: "Gamma G",
      value: gammaG,
      onChange: onGammaGChange,
      min: 1,
      max: 2.2,
      step: 0.05,
      def: 1,
      intent: "success" as const,
    },
    {
      icon: "full-circle",
      label: "Gamma B",
      value: gammaB,
      onChange: onGammaBChange,
      min: 1,
      max: 2.2,
      step: 0.05,
      def: 1,
      intent: "primary" as const,
    },
  ];

  return (
    <div style={{ opacity: disableGamma ? 0.6 : 1, pointerEvents: disableGamma ? "none" : "auto" }}>
      {items.map((s) => (
        <ControlGroup
          key={s.label}
          fill
          className="bp5-condensed"
          style={{ alignItems: "center", gap: 4 }}
        >
          <Icon icon={s.icon} intent={s.intent} title={s.label} />
          <Slider
            className="bp5-small"
            min={s.min}
            max={s.max}
            stepSize={s.step}
            labelRenderer={false}
            value={s.value}
            onChange={s.onChange}
          />
          <Tag minimal>{(s.value || 1).toFixed(2)}</Tag>
          <Button
            className="bp5-small bp5-minimal"
            icon="reset"
            onClick={() => s.onChange(s.def)}
            aria-label={`Reset ${s.label}`}
          />
        </ControlGroup>
      ))}
    </div>
  );
};

export default GammaControls;
