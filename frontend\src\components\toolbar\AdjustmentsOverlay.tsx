import React, { useState } from "react";
import { Button, Overlay2 } from "@blueprintjs/core";
import { Rnd } from "react-rnd";
import ActionButtons from "./ActionButtons";
import SliderControls from "./AdjustmentControls";
import GammaControls from "./GammaControls";

interface Props {
  isOpen: boolean;
  onClose: () => void;
  brightness: number;
  contrast: number;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
  disableGamma: boolean;
  onBrightnessChange: (v: number) => void;
  onContrastChange: (v: number) => void;
  onSharpnessChange: (v: number) => void;
  onGammaRChange: (v: number) => void;
  onGammaGChange: (v: number) => void;
  onGammaBChange: (v: number) => void;
  canUndo: boolean;
  onUndo: () => void;
  onSave: () => void;
}

const AdjustmentsOverlay: React.FC<Props> = (props) => {
  const {
    isOpen,
    onClose,
    brightness,
    contrast,
    sharpness,
    gammaR,
    gammaG,
    gammaB,
    disableGamma,
    onBrightnessChange,
    onContrastChange,
    onSharpnessChange,
    onGammaRChange,
    onGammaGChange,
    onGammaBChange,
    canUndo,
    onUndo,
    onSave,
  } = props;

  const [transparent, setTransparent] = useState(false);

  return (
    <Overlay2 isOpen={isOpen} onClose={onClose} hasBackdrop>
      <Rnd
        default={{
          x: Math.max(0, window.innerWidth / 2 - 180),
          y: Math.max(0, window.innerHeight / 2 - 160),
          width: 360,
          height: 320,
        }}
        bounds="window"
        enableResizing={false}
        dragHandleClassName="bp5-dialog-header"
      >
        <div
          className="bp5-dialog"
          style={{
            margin: 0,
            width: "100%",
            background: "var(--pt-app-background-color, #fff)",
            border: "1px solid var(--pt-divider-black, #d9d9d9)",
            opacity: transparent ? 0.35 : 1,
          }}
        >
          <div
            className="bp5-dialog-header bp5-small"
            style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
          >
            <h4 className="bp5-heading" style={{ margin: 0 }}>
              Adjustments
            </h4>
            <div style={{ display: "flex", gap: 4 }}>
              <Button
                className="bp5-small bp5-minimal"
                icon="eye-open"
                active={transparent}
                onClick={() => setTransparent((v) => !v)}
                title="Peek through"
              />
              <Button className="bp5-small bp5-minimal" icon="small-cross" onClick={onClose} />
            </div>
          </div>
          <div className="bp5-dialog-body" style={{ padding: 8 }}>
            <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
              <SliderControls
                brightness={brightness}
                contrast={contrast}
                sharpness={sharpness}
                onBrightnessChange={onBrightnessChange}
                onContrastChange={onContrastChange}
                onSharpnessChange={onSharpnessChange}
              />
              <GammaControls
                gammaR={gammaR}
                gammaG={gammaG}
                gammaB={gammaB}
                disableGamma={disableGamma}
                onGammaRChange={onGammaRChange}
                onGammaGChange={onGammaGChange}
                onGammaBChange={onGammaBChange}
              />
              <ActionButtons canUndo={canUndo} onUndo={onUndo} onSave={onSave} />
            </div>
          </div>
        </div>
      </Rnd>
    </Overlay2>
  );
};

export default AdjustmentsOverlay;

