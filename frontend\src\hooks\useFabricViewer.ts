import { use<PERSON><PERSON>back, useRef, useState, useMemo } from "react";
import { Canvas } from "fabric";
import { createSaveHand<PERSON> } from "@/lib/fabric/operations";
import { setupCanvasEventListeners } from "@/lib/fabric/events";
import type {
  CropData,
  ImageViewerProps,
  FabricObjectState,
  FilterParams,
  UndoAction,
  TransformState,
  FilterState,
} from "@/models";
import { setupImageCanvas } from "@/lib/fabric/canvas";
import { createUndoHandler } from "@/lib/fabric/operations";
import {
  createBrightnessHandler,
  createContrastHandler,
  createGrayscaleHandler,
  createInvertHandler,
  createSharpnessHandler,
  createGammaRHandler,
  createGammaGHandler,
  createGammaBHandler,
  createRotateHandler,
  createFlipHorizontalHandler,
  createFlipVerticalHandler,
  createCropHandler,
} from "@/lib/fabric/operations";
import {
  getAnnotationColorDefault,
  getStrokeWidthDefault,
  getLineStyleDefault,
  getFontSizeDefault,
  getBoldDefault,
  getItalicDefault,
  getUnderlineDefault,
  getFontFamilyDefault,
  getMagnifierZoomDefault,
  getMagnifierRadiusDefault,
} from "@/lib/fabric/utils";

/** Composes Fabric viewer state: setup, filters, transforms, crop, undo, save */
export const useFabricViewer = ({
  data,
  onResizeNeeded,
}: ImageViewerProps & {
  containerRef?: React.RefObject<HTMLElement | null>;
  onResizeNeeded?: () => void;
}) => {
  const fabricCanvas = useRef<Canvas | null>(null);
  const eventDisposers = useRef<(() => void)[]>([]);
  const initialObjectCount = useRef(0);
  const originalImageUrl = useRef("");
  const objectStates = useRef(new Map<string, FabricObjectState>());
  const isInitializingRef = useRef(false);

  // Consolidated Undo Tracking State
  const [undoStack, setUndoStack] = useState<UndoAction[]>([]);
  const isUndoingRef = useRef<boolean>(false);

  // Consolidated Filter State
  const initialFilters: FilterState = {
    brightness: data.viewer.fabricConfigs.brightness,
    contrast: data.viewer.fabricConfigs.contrast,
    grayscale: data.viewer.fabricConfigs.grayscale,
    invert: data.viewer.fabricConfigs.invert,
    sharpness: data.viewer.fabricConfigs.sharpness,
    gammaR: data.viewer.fabricConfigs.gammaR,
    gammaG: data.viewer.fabricConfigs.gammaG,
    gammaB: data.viewer.fabricConfigs.gammaB,
  };
  const [filters, setFilters] = useState<FilterState>(initialFilters);

  // Consolidated Transform State
  const [transformState, setTransformState] = useState<TransformState>(
    data.viewer.fabricConfigs.transformState!
  );

  // Consolidated Crop State
  const [cropData, setCropData] = useState<CropData>(data.viewer.fabricConfigs.cropData!);

  // Consolidated Undo Handlers
  const handleUndo = createUndoHandler(
    fabricCanvas,
    undoStack,
    setUndoStack,
    initialObjectCount,
    isUndoingRef
  );

  const disableUndoTracking = () => {
    isUndoingRef.current = true;
  };
  const enableUndoTracking = () => {
    isUndoingRef.current = false;
  };

  const addUndoAction = (action: UndoAction) => {
    if (isUndoingRef.current) return;
    setUndoStack((prev) => [...prev, action]);
  };

  // Consolidated Filter Handlers
  const updateFilter = useCallback((keyOrConfig: string | object, value?: number | boolean) => {
    if (typeof keyOrConfig === "object") {
      setFilters((prev) => ({ ...prev, ...keyOrConfig }));
    } else if (value !== undefined) {
      setFilters((prev) => ({ ...prev, [keyOrConfig]: value }));
    }
  }, []);

  const filterHandlers = useMemo(() => {
    const numericUpdateFilter = (key: string, value: number) => updateFilter(key, value);
    const booleanUpdateFilter = (key: string, value: boolean) => updateFilter(key, value);

    return {
      handleBrightnessChange: createBrightnessHandler(fabricCanvas, numericUpdateFilter),
      handleContrastChange: createContrastHandler(fabricCanvas, numericUpdateFilter),
      handleGrayscaleChange: createGrayscaleHandler(fabricCanvas, booleanUpdateFilter),
      handleInvertChange: createInvertHandler(fabricCanvas, booleanUpdateFilter),
      handleSharpnessChange: createSharpnessHandler(fabricCanvas, numericUpdateFilter),
      handleGammaRChange: createGammaRHandler(fabricCanvas, numericUpdateFilter),
      handleGammaGChange: createGammaGHandler(fabricCanvas, numericUpdateFilter),
      handleGammaBChange: createGammaBHandler(fabricCanvas, numericUpdateFilter),
    };
  }, [updateFilter, fabricCanvas]);

  // Consolidated Transform Handlers
  const handleRotate = createRotateHandler(fabricCanvas, setTransformState, onResizeNeeded);
  const handleFlipHorizontal = createFlipHorizontalHandler(fabricCanvas, setTransformState);
  const handleFlipVertical = createFlipVerticalHandler(fabricCanvas, setTransformState);
  const handleCrop = createCropHandler(fabricCanvas, cropData, setCropData);

  // Initializes or re-initializes the Fabric canvas for the given image
  const setupCanvas = useCallback(
    async (canvasElement: HTMLCanvasElement, imageSource: string) => {
      if (isInitializingRef.current) return;
      isInitializingRef.current = true;

      const result = await setupImageCanvas({
        canvasElement,
        imageUrl: imageSource,
        annotations: data.viewer.fabricConfigs.annotations,
        filters: filters as FilterParams,
        existingCanvas: fabricCanvas.current,
        transformState: data.viewer.fabricConfigs.transformState,
      });

      fabricCanvas.current = result.canvas;
      originalImageUrl.current = imageSource;

      if (data.viewer.fabricConfigs.cropData) {
        const loadedCropData = data.viewer.fabricConfigs.cropData as CropData;
        if (loadedCropData.isCropped !== cropData.isCropped) {
          setCropData(loadedCropData);
        }
      }
      const canvas = fabricCanvas.current;
      (canvas as any).annotationColor = getAnnotationColorDefault();
      (canvas as any).annotationSettings = {
        strokeWidth: getStrokeWidthDefault(),
        lineStyle: getLineStyleDefault(),
      };
      (canvas as any).textSettings = {
        fontSize: getFontSizeDefault(),
        fontWeight: getBoldDefault() ? "bold" : "normal",
        fontStyle: getItalicDefault() ? "italic" : "normal",
        underline: getUnderlineDefault(),
        fontFamily: getFontFamilyDefault(),
        fill: getAnnotationColorDefault(),
      };
      (canvas as any).magnifierSettings = {
        zoom: getMagnifierZoomDefault(),
        radius: getMagnifierRadiusDefault(),
      };
      initialObjectCount.current = canvas.getObjects().length;

      // Rewire Fabric event listeners so handlers reflect current closures/state
      eventDisposers.current.forEach((dispose) => dispose());
      const undoTrackingState = {
        isUndoingRef,
        addUndoAction,
      };
      eventDisposers.current = setupCanvasEventListeners(
        canvas,
        undoTrackingState,
        objectStates,
        data.viewer.fabricConfigs.calibrationData
      );

      isInitializingRef.current = false;
    },
    [
      data.viewer.fabricConfigs.annotations,
      data.viewer.fabricConfigs.cropData, // Backend crop data (for initialization)
      data.viewer.fabricConfigs.transformState,
      data.viewer.fabricConfigs.calibrationData,
      filters,
      cropData, // Local crop data (for real-time updates)
    ]
  );

  const handleSave = createSaveHandler(fabricCanvas, data.id, filters, cropData, transformState);

  return {
    canvas: fabricCanvas,
    setupCanvas,
    // Filter state and handlers
    brightness: filters.brightness,
    contrast: filters.contrast,
    grayscale: filters.grayscale,
    invert: filters.invert,
    sharpness: filters.sharpness,
    gammaR: filters.gammaR,
    gammaG: filters.gammaG,
    gammaB: filters.gammaB,
    ...filterHandlers,
    // Transform state and handlers
    transformState,
    setTransformState,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    // Undo state and handlers
    undoStack,
    canUndo: undoStack.length > 0,
    isUndoingRef,
    handleUndo,
    addUndoAction,
    disableUndoTracking,
    enableUndoTracking,
    // Crop state and handlers
    cropData,
    setCropData,
    handleCrop,
    handleSave,
  };
};
