import { useEffect, useRef, useState } from "react";
import { Types, Enums, imageLoader, RenderingEngine } from "@cornerstonejs/core";
import FabricToolbar from "@/components/toolbar/FabricToolbar";
import { initializeCornerstone, setup2dViewport, loadDicomStack } from "@/lib/dicom/core";
import type { ImageViewerProps } from "@/models";
import { useFabricViewer } from "@/hooks";
import { useResponsiveCanvas } from "@/hooks";

import { RENDER_ENGINE_ID_2D, VIEWPORT_ID_2D } from "@/lib/dicom/constants";

const Dicom2DViewer: React.FC<ImageViewerProps> = ({ data, onShapeCreated }) => {
  const cornerstoneRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasFabricCanvas, setHasFabricCanvas] = useState(false);

  const {
    canvas: fabricCanvas,
    setupCanvas,
    brightness,
    contrast,
    sharpness,
    gammaR,
    gammaG,
    gammaB,
    grayscale,
    invert,
    handleBrightnessChange,
    handleContrastChange,
    handleGrayscaleChange,
    handleInvertChange,
    handleSharpnessChange,
    handleGammaRChange,
    handleGammaGChange,
    handleGammaBChange,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleUndo,
    handleCrop,
    handleSave,
    disableUndoTracking,
    enableUndoTracking,
    canUndo,
    cropData,
  } = useFabricViewer({
    data,
    onResizeNeeded: () => resizeCanvas(),
  });

  const { resizeCanvas } = useResponsiveCanvas({
    fabricCanvas,
    containerRef,
    cropData,
  });
  const captureAndTransferToFabric = () => {
    if (!cornerstoneRef.current || !canvasRef.current) return;
    const cornerstoneCanvas = cornerstoneRef.current.querySelector(
      "canvas"
    ) as HTMLCanvasElement | null;
    if (!cornerstoneCanvas) return;
    cornerstoneCanvas.toBlob(
      async (blob) => {
        if (!blob) return;
        const url = URL.createObjectURL(blob);
        await setupCanvas(canvasRef.current!, url);
        setHasFabricCanvas(true);
        resizeCanvas();
        URL.revokeObjectURL(url);
      },
      "image/png",
      1
    );
  };
  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);
    return () => {
      renderingEngineRef.current?.destroy();
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !cornerstoneRef.current) return;
    const element = cornerstoneRef.current;
    async function setupViewer() {
      const renderingEngineId = RENDER_ENGINE_ID_2D;
      const currentViewportId = VIEWPORT_ID_2D;
      const image = await imageLoader.loadAndCacheImage(data.viewer.imageUrl);
      const { width: columns, height: rows } = image;
      element.style.width = `${columns}px`;
      element.style.height = `${rows}px`;

      const renderingEngine = new RenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;
      const viewport = setup2dViewport(renderingEngine, element, currentViewportId);
      viewportRef.current = viewport;
      await loadDicomStack(viewport, data.viewer.imageUrl);
      const handleImageRendered = () => {
        captureAndTransferToFabric();
        resizeCanvas();
        element.removeEventListener(Enums.Events.IMAGE_RENDERED, handleImageRendered);
      };
      element.addEventListener(Enums.Events.IMAGE_RENDERED, handleImageRendered);
    }
    setupViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, data.viewer.imageUrl]);

  return (
    <div className="dicom-2d-viewer" id="dicom-2d-viewer-container">
      <div
        ref={containerRef}
        className={`viewer-container ${cropData?.isCropped ? "cropped" : ""}`}
      >
        {!hasFabricCanvas && (
          <div
            ref={cornerstoneRef}
            className="cornerstone-element"
            style={{ position: "absolute", visibility: "hidden" }}
          />
        )}
        <canvas ref={canvasRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        fabricConfigs={{
          ...data.viewer.fabricConfigs,
          brightness,
          contrast,
          sharpness,
          gammaR,
          gammaG,
          gammaB,
          grayscale,
          invert,
        }}
        handlers={{
          filter: {
            handleBrightnessChange,
            handleContrastChange,
            handleGrayscaleChange,
            handleInvertChange,
            handleSharpnessChange,
            handleGammaRChange,
            handleGammaGChange,
            handleGammaBChange,
          },
          transform: {
            handleRotate,
            handleFlipHorizontal,
            handleFlipVertical,
          },
          actions: {
            handleUndo,
            handleSave,
            handleCrop,
          },
          tracking: {
            disableUndoTracking,
            enableUndoTracking,
          },
        }}
        state={{
          canUndo,
          cropData,
        }}
        config={{
          disableGrayscale: true,
          disableGamma: true,
        }}
        onShapeCreated={onShapeCreated}
      />
    </div>
  );
};

export default Dicom2DViewer;
