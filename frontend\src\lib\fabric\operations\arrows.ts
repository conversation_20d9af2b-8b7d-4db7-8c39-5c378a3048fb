import { Canvas, Line } from "fabric";
import { v4 as uuidv4 } from "uuid";

/*
Creates an arrow annotation as a single Line object with arrow head rendering.
Uses custom _render method to draw triangle head at start point.
Avoids Fabric.js Group selection issues by using single selectable object.
*/
export const createArrow = (
  start: { x: number; y: number },
  config: any
): Line & { id: string; headSize: number } => {
  const line = new Line([start.x, start.y, start.x, start.y], {
    stroke: config.stroke,
    ...config,
  });

  (line as any).id = uuidv4();
  (line as any).headSize = Math.max(6, Number(config.arrowHeadSize ?? 14));
  (line as any).customType = "arrow";

  const originalToObject = line.toObject;
  (line as any).toObject = function (propertiesToInclude?: any[]) {
    const customProps = ["headSize", "customType", "id"];
    const allProps = propertiesToInclude ? [...propertiesToInclude, ...customProps] : customProps;
    return originalToObject.call(this, allProps as any);
  };

  // Override render method to draw arrow head at start point
  const originalRender = line._render;
  (line as any)._render = function (ctx: CanvasRenderingContext2D) {
    originalRender.call(this, ctx);

    const headSize = (this as any).headSize || 14;
    const points = this.calcLinePoints();
    const x1 = points.x1;
    const y1 = points.y1;
    const x2 = points.x2;
    const y2 = points.y2;

    if (Math.abs(x2 - x1) < 1 && Math.abs(y2 - y1) < 1) return;

    const angle = Math.atan2(y2 - y1, x2 - x1);

    ctx.save();
    ctx.translate(x1, y1);
    ctx.rotate(angle);
    ctx.fillStyle = this.stroke || "#ff0000";
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(headSize, -headSize / 2);
    ctx.lineTo(headSize, headSize / 2);
    ctx.closePath();
    ctx.fill();
    ctx.restore();
  };

  return line as Line & { id: string; headSize: number };
};

/*
Updates arrow geometry during drag by setting line coordinates.
Arrow head automatically redraws at start point via custom render method.
*/
export const updateArrowSize = (
  line: Line,
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number }
): void => {
  line.set({
    x1: startPoint.x,
    y1: startPoint.y,
    x2: currentPoint.x,
    y2: currentPoint.y,
  });

  line.setCoords();
  line.canvas?.requestRenderAll();
};

/*
Updates arrow head size based on stroke width.
Ensures arrow head scales proportionally with line thickness.
*/
export const updateArrowHeadSize = (arrow: any, strokeWidth: number): void => {
  if (!isArrow(arrow)) return;

  const newHeadSize = Math.max(8, strokeWidth * 7);
  (arrow as any).headSize = newHeadSize;

  // Force re-render to apply new head size
  (arrow as any).dirty = true;
};

/*
Refreshes arrow display after line modification.
Arrow head automatically updates via custom render method.
*/
export const updateArrowOnModify = (canvas: Canvas, line: Line): void => {
  line.setCoords();
  canvas.requestRenderAll();
};

export const isArrow = (obj: any): obj is Line => {
  return (
    ((obj as any)?.name === "arrow" || (obj as any)?.customType === "arrow") &&
    (obj as any)?.type === "line"
  );
};

/*
Restores custom render method for arrows after loading from JSON.
Fabric.js doesn't serialize custom functions, so we need to reattach them.
*/
export const restoreArrowRender = (line: Line) => {
  const originalToObject = line.toObject;
  (line as any).toObject = function (propertiesToInclude?: any[]) {
    const customProps = ["headSize", "customType", "id"];
    const allProps = propertiesToInclude ? [...propertiesToInclude, ...customProps] : customProps;
    return originalToObject.call(this, allProps as any);
  };

  const originalRender = line._render;
  (line as any)._render = function (ctx: CanvasRenderingContext2D) {
    originalRender.call(this, ctx);

    const headSize = (this as any).headSize || 14;
    const points = this.calcLinePoints();
    const x1 = points.x1;
    const y1 = points.y1;
    const x2 = points.x2;
    const y2 = points.y2;

    if (Math.abs(x2 - x1) < 1 && Math.abs(y2 - y1) < 1) return;

    const angle = Math.atan2(y2 - y1, x2 - x1);

    ctx.save();
    ctx.translate(x1, y1);
    ctx.rotate(angle);
    ctx.fillStyle = this.stroke || "#ff0000";
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(headSize, -headSize / 2);
    ctx.lineTo(headSize, headSize / 2);
    ctx.closePath();
    ctx.fill();
    ctx.restore();
  };
};
