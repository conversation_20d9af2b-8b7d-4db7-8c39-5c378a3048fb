import { Canvas } from "fabric";
import { CropData } from "./CropData";
import { CalibrationData } from "./CalibrationData";
import { TransformState } from "./TransformState";

export interface UseFabricToolsProps {
  fabricCanvas: React.MutableRefObject<Canvas | null>;
  cropData?: CropData;
  onShapeCreated?: () => void;
  onCrop?: () => void;
  disableUndoTracking?: () => void;
  enableUndoTracking?: () => void;
  showCalibrationModal?: () => void;
  calibrationData?: CalibrationData;
  onCalibrationPrompt?: () => void;
}

export interface ImageTransformsState {
  transformState: TransformState;
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>;
  handleRotate: () => void;
  handleFlipHorizontal: () => void;
  handleFlipVertical: () => void;
}

export interface CropManagementState {
  cropData: CropData;
  setCropData: React.Dispatch<React.SetStateAction<CropData>>;
  handleCrop: () => void;
}
