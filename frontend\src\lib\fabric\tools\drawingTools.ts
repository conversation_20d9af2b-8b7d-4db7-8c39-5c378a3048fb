import { <PERSON>vas, Textbox, Rect, Line, Circle } from "fabric";
import type { ToolMode, FabricMeasurementLine, CalibrationData } from "@/models";
import { constrainTo<PERSON><PERSON><PERSON>, transformPointer, getToolConfig } from "./toolConfigs";
import {
  createArrow,
  updateArrowSize,
  createMeasurementLine,
  updateMeasurementSize,
  updateMeasurementText,
  createProtractor,
  updateProtractorSize,
  finalizeProtractor,
  createTextbox,
} from "@/lib/fabric/operations";
import { v4 as uuidv4 } from "uuid";

/*
Creates and starts a drawing interaction. Returns shape and startPoint for continuous
updates, or null if tool is not drawable. Handles special cases like text editing.
*/
export const startDrawing = (
  mode: ToolMode,
  pointer: { x: number; y: number },
  canvas: Canvas,
  isCropped?: boolean,
  onShapeCreated?: () => void
) => {
  if (!mode || mode === "select") {
    return null;
  }

  if (mode === "crop" && isCropped) {
    return null;
  }

  const constrainedPointer = constrainToCanvas(pointer, canvas);

  if (mode === "text") {
    handleTextToolClick(constrainedPointer, canvas, onShapeCreated);
    return null;
  }

  const transformedPointer = transformPointer(constrainedPointer, canvas);
  const selectedColor: string = (canvas as any).annotationColor || "red";
  const config = getToolConfig(mode) as any;

  // Highlight should not be affected by annotation color or settings - it has fixed styling
  if (mode !== "highlight") {
    config.stroke = selectedColor;

    // Apply annotation settings from bottom toolbar if available
    const annotationSettings = (canvas as any).annotationSettings;
    if (annotationSettings) {
      // Only apply settings to tools that should be affected
      if (mode !== "protractor" && mode !== "crop") {
        if (annotationSettings.strokeWidth !== undefined) {
          config.strokeWidth = annotationSettings.strokeWidth;
          // Special handling for arrows - scale head size with stroke width
          if (mode === "arrow") {
            config.arrowHeadSize = Math.max(8, annotationSettings.strokeWidth * 7);
          }
        }

        // Apply line style (strokeDashArray)
        if (annotationSettings.lineStyle !== undefined) {
          const getStrokeDashArray = (lineStyle: string): number[] | undefined => {
            switch (lineStyle) {
              case "solid":
                return undefined;
              case "dashed":
                return [10, 5];
              case "dotted":
                return [2, 3];
              default:
                return undefined;
            }
          };
          config.strokeDashArray = getStrokeDashArray(annotationSettings.lineStyle);
        }
      }
    }
  }

  const common = { id: uuidv4(), ...config };

  let shape;
  switch (mode) {
    case "rect":
    case "highlight":
      shape = new Rect({
        left: transformedPointer.x,
        top: transformedPointer.y,
        width: 1,
        height: 1,
        ...common,
      });
      break;
    case "line":
      shape = new Line(
        [transformedPointer.x, transformedPointer.y, transformedPointer.x, transformedPointer.y],
        { ...common }
      );
      break;
    case "circle":
      shape = new Circle({
        left: transformedPointer.x,
        top: transformedPointer.y,
        radius: 1,
        ...common,
      });
      break;
    case "crop":
      shape = new Rect({
        left: transformedPointer.x,
        top: transformedPointer.y,
        ...common,
      });
      break;
    case "measure":
      shape = createMeasurementLine({ x: transformedPointer.x, y: transformedPointer.y }, common);
      break;
    case "calibrate":
      shape = new Rect({
        left: transformedPointer.x,
        top: transformedPointer.y,
        width: 1,
        height: 1,
        ...common,
      });
      break;
    case "arrow":
      shape = createArrow({ x: transformedPointer.x, y: transformedPointer.y }, common);
      break;
    case "protractor":
      shape = createProtractor(
        canvas,
        { x: transformedPointer.x, y: transformedPointer.y },
        common
      );
      break;
    default:
      return null;
  }

  if (!shape) return null;
  const EXTRA_GLOBAL_SELECTION_PROPS = ["perPixelTargetFind"];

  // protractor-specific props to persist
  const PROTRACTOR_SELECTION_PROPS = [
    "id",
    "parentId",
    "handleOf",
    "rayBId",
    "textId",
    "handleAId",
    "handleBId",
    "vertexHandleId",
    "rotateHandleId",
    "vertex",
    "phase",
    "arcRadius",
    "handleRadius",
    "__selfAdded",
    "name",
    "customType",
    "excludeFromActiveGroup",
    "selectable",
    "evented",
    "hasControls",
    "hasBorders",
    "hoverCursor",
    "moveCursor",
  ];

  // Add custom toObject override for proper property serialization
  if (!(shape as any)._hasCustomToObject) {
    const originalToObject = (shape as any).toObject;
    (shape as any).toObject = function (propertiesToInclude?: any[]) {
      const incoming = propertiesToInclude ?? [];
      const merged = Array.from(
        new Set([...incoming, ...EXTRA_GLOBAL_SELECTION_PROPS, ...PROTRACTOR_SELECTION_PROPS])
      );
      return originalToObject.call(this, merged);
    };
    (shape as any)._hasCustomToObject = true;
  }

  if (!(shape as any).__selfAdded) {
    canvas.add(shape);
  }

  return { shape, startPoint: transformedPointer };
};

/*
Updates shape geometry during drawing and handles special cases like measurement text.
Combines geometry updates with tool-specific behaviors like calibration.
*/
export const updateDrawing = (
  pointer: { x: number; y: number },
  mode: ToolMode,
  canvas: Canvas,
  shape: any,
  startPoint: { x: number; y: number },
  calibrationData?: CalibrationData
) => {
  if (!shape || !startPoint) return;

  const constrainedPointer = constrainToCanvas(pointer, canvas);
  const currentPoint = transformPointer(constrainedPointer, canvas);
  // Update shape geometry based on tool type
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();
  const width = Math.abs(currentPoint.x - startPoint.x);
  const height = Math.abs(currentPoint.y - startPoint.y);
  const left = Math.min(startPoint.x, currentPoint.x);
  const top = Math.min(startPoint.y, currentPoint.y);

  switch (mode) {
    case "text":
      break;
    case "rect":
    case "highlight":
    case "crop":
      shape.set({
        left: left,
        top: top,
        width: Math.max(1, width),
        height: Math.max(1, height),
      });
      break;
    case "line":
    case "calibrate":
      shape.set({
        x1: startPoint.x,
        y1: startPoint.y,
        x2: currentPoint.x,
        y2: currentPoint.y,
      });
      break;
    case "measure":
      updateMeasurementSize(shape as Line, startPoint, currentPoint);
      break;
    case "arrow":
      updateArrowSize(shape as Line, startPoint, currentPoint);
      break;
    case "protractor":
      updateProtractorSize(shape as any, currentPoint, canvas);
      break;
    case "circle": {
      const maxRadius = Math.min(
        (canvasWidth - left) / 2,
        (canvasHeight - top) / 2,
        width / 2,
        height / 2
      );
      const radius = Math.max(1, maxRadius);
      shape.set({
        left: left,
        top: top,
        radius: radius,
      });
      break;
    }
  }

  // Handle measurement text with calibration data
  if (mode === "measure" && calibrationData) {
    const line = shape as FabricMeasurementLine;
    updateMeasurementText(canvas, line, calibrationData);
  }

  canvas.renderAll();
};

/*
If clicking an existing textbox, start editing it; otherwise create a new textbox
at the transformed pointer and focus it.
*/
export const handleTextToolClick = (
  pointer: { x: number; y: number },
  canvas: Canvas,
  onShapeCreated?: () => void
) => {
  const objects = canvas.getObjects();
  const clickedObject = objects.find((objectOnCanvas) => {
    if (objectOnCanvas.type === "textbox") {
      const objectBounds = objectOnCanvas.getBoundingRect();
      return (
        pointer.x >= objectBounds.left &&
        pointer.x <= objectBounds.left + objectBounds.width &&
        pointer.y >= objectBounds.top &&
        pointer.y <= objectBounds.top + objectBounds.height
      );
    }
    return false;
  });

  if (clickedObject) {
    canvas.setActiveObject(clickedObject);
    (clickedObject as Textbox).enterEditing();
    return true;
  }

  const transformedPointer = transformPointer(pointer, canvas);
  const selectedColor: string = (canvas as any).annotationColor || "red";
  const config = getToolConfig("text") as any;

  // Apply settings from bottom toolbar if available
  const textSettings = (canvas as any).textSettings;
  if (textSettings) {
    config.fontSize = textSettings.fontSize;
    config.fill = textSettings.fill;
    config.fontWeight = textSettings.fontWeight;
    config.fontStyle = textSettings.fontStyle;
    config.underline = textSettings.underline;
    config.fontFamily = textSettings.fontFamily;
  } else {
    config.fill = selectedColor;
  }

  const shape = createTextbox(transformedPointer, config);

  canvas.add(shape);
  canvas.setActiveObject(shape);
  const textbox = shape as Textbox;

  textbox.enterEditing();
  textbox.selectAll();

  if (onShapeCreated) onShapeCreated();
  return true;
};

/*
Completes the drawing interaction. Handles final setup like crop triggers,
measurement text creation, and shape completion callbacks.
*/
export const completeDrawing = (
  activeMode: ToolMode,
  currentShape: any,
  canvas: Canvas,
  onCrop?: () => void,
  onShapeCreated?: () => void,
  calibrationData?: CalibrationData
) => {
  if (!currentShape) return;

  if (activeMode === "crop" && onCrop) onCrop();

  if (activeMode === "measure" && calibrationData) {
    const line = currentShape as FabricMeasurementLine;
    updateMeasurementText(canvas, line, calibrationData);
  }

  if (activeMode === "protractor") {
    finalizeProtractor(canvas, currentShape as any);
  }

  if (onShapeCreated && activeMode !== "crop") onShapeCreated();
};
