import { useEffect, useRef } from "react";
import FabricToolbar from "@/components/toolbar/FabricToolbar";
import type { ImageViewerProps } from "@/models";
import { useFabricViewer } from "@/hooks";
import { useResponsiveCanvas } from "@/hooks";

const ImageViewer: React.FC<ImageViewerProps> = ({ data, onShapeCreated }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const {
    canvas: fabricCanvas,
    setupCanvas,
    brightness,
    contrast,
    sharpness,
    gammaR,
    gammaG,
    gammaB,
    grayscale,
    invert,
    handleBrightnessChange,
    handleContrastChange,
    handleGrayscaleChange,
    handleInvertChange,
    handleSharpnessChange,
    handleGammaRChange,
    handleGammaGChange,
    handleGammaBChange,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleUndo,
    handleCrop,
    handleSave,
    disableUndoTracking,
    enableUndoTracking,
    canUndo,
    cropData,
  } = useFabricViewer({
    data,
    onResizeNeeded: () => resizeCanvas(),
  });

  const { resizeCanvas } = useResponsiveCanvas({
    fabricCanvas,
    containerRef,
    cropData,
  });

  useEffect(() => {
    async function setupViewer() {
      if (!canvasRef.current) return;
      await setupCanvas(canvasRef.current, data.viewer.imageUrl);
      resizeCanvas();
    }
    setupViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data.viewer.imageUrl]);

  return (
    <div className="image-viewer" id="image-viewer-container">
      <div
        ref={containerRef}
        className={`viewer-container ${cropData?.isCropped ? "cropped" : ""}`}
      >
        <canvas ref={canvasRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        fabricConfigs={{
          ...data.viewer.fabricConfigs,
          brightness,
          contrast,
          sharpness,
          gammaR,
          gammaG,
          gammaB,
          grayscale,
          invert,
        }}
        handlers={{
          filter: {
            handleBrightnessChange,
            handleContrastChange,
            handleGrayscaleChange,
            handleInvertChange,
            handleSharpnessChange,
            handleGammaRChange,
            handleGammaGChange,
            handleGammaBChange,
          },
          transform: {
            handleRotate,
            handleFlipHorizontal,
            handleFlipVertical,
          },
          actions: {
            handleUndo,
            handleSave,
            handleCrop,
          },
          tracking: {
            disableUndoTracking,
            enableUndoTracking,
          },
        }}
        state={{
          canUndo,
          cropData,
        }}
        config={{}}
        onShapeCreated={onShapeCreated}
      />
    </div>
  );
};

export default ImageViewer;
