* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.viewer-base {
  width: 100%;
  height: 100%;
  background: #000;
  color: white;
  position: relative;
  overflow: hidden;
}

.viewer-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;

  overflow: hidden;

  &.cropped {
    background: transparent;

    .fabric-canvas {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }
}

.cornerstone-element {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #000;
  object-fit: contain;
}

.dicom-2d-viewer {
  @extend .viewer-base;
}

.dicom-3d-viewer {
  @extend .viewer-base;

  .mode-switcher {
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 12px;
    color: white;
    cursor: pointer;
    z-index: 10;

    &-item {
      &.active {
        color: #4caf50;
      }

      &.inactive {
        color: #888;
      }
    }

    &-separator {
      margin: 0 5px;
    }
  }
}

.image-viewer {
  @extend .viewer-base;
}

.patient-viewer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;

  .patient-viewer-content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .viewer-panel {
    flex: 1;
    min-width: 0;
    background: white;
    position: relative;
    height: 100%;
    overflow: hidden;

    .empty-state,
    .loading-state,
    .error-state {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #666;
    }
  }
}
