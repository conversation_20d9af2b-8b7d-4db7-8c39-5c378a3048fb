import { Line, Triangle, Circle, FabricImage } from "fabric";

export interface FabricMeasurementLine extends Line {
  id: string;
  measurementText?: string;
}

export type ArrowLine = Line & {
  id: string;
  name?: string;
  head?: Triangle;
  headSize?: number;
};

export interface MagnifierState {
  lensImage?: FabricImage;
  clipCircle?: Circle;
  originalImage?: FabricImage;
  scale: number;
  radius: number;
  eventCleanup?: () => void;
  lastX?: number;
  lastY?: number;
}
