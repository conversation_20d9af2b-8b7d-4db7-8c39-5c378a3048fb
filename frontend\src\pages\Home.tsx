import { <PERSON><PERSON>, <PERSON> } from "@blueprintjs/core";
import { useNavigate } from "react-router-dom";

const Home: React.FC = () => {
  const navigate = useNavigate();
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "100vh",
      }}
    >
      <Card elevation={2} style={{ padding: 24, minWidth: 360, textAlign: "center" }}>
        <h1 className="bp5-heading" style={{ marginBottom: 8 }}>
          CSOI Application
        </h1>
        <p style={{ opacity: 0.8, marginBottom: 16 }}>Choose Patient:</p>
        <Button intent="primary" size="large" onClick={() => navigate("/patient/P001/fileviewer")}>
          <PERSON>
        </But<PERSON>>
      </Card>
    </div>
  );
};

export default Home;
