import { Canvas } from "fabric";
import { deactivateMagnifierForTool, activateMagnifier } from "@/lib/fabric/operations/magnifier";

// Objects that should never be selectable
const nonSelectable = ["backgroundImage", "measurementText", "arrowHead"];
/*
Universal tool mode manager - only handles selectability and canvas modes.
All object properties come from toolConfigs.ts as single source of truth.
*/
export const setToolMode = (toolMode: string, canvas: Canvas) => {
  const activeObject = canvas.getActiveObject();
  const isSelectMode = toolMode === "select";

  // Handle magnifier
  if (toolMode === "magnifier") {
    deactivateMagnifierForTool(canvas);
    activateMagnifier(canvas);
  } else {
    deactivateMagnifierForTool(canvas);
  }

  // Set canvas modes
  canvas.selection = isSelectMode;
  canvas.defaultCursor = isSelectMode ? "default" : "crosshair";
  canvas.isDrawingMode = toolMode === "freehand";

  // Discard selection when not in select mode
  if (!isSelectMode) {
    canvas.discardActiveObject();
  } else if (activeObject) {
    canvas.setActiveObject(activeObject);
  }

  canvas.forEachObject((obj) => {
    const objectName = (obj as any)?.name;
    const shouldBeSelectable = isSelectMode && !nonSelectable.includes(objectName);

    obj.selectable = shouldBeSelectable;
    obj.evented = shouldBeSelectable;
    obj.setCoords();
  });

  canvas.renderAll();
};
