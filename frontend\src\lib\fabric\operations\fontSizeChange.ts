import { Canvas } from "fabric";

/*
Updates font size for selected text objects on the canvas.
Only affects textbox objects, ignores other shape types.
*/
export const changeSelectedObjectsFontSize = (canvas: Canvas, fontSize: number): void => {
  const activeObject = canvas.getActiveObject();

  if (!activeObject) return;

  const selectedObjects =
    activeObject.type === "activeselection" ? (activeObject as any).getObjects() : [activeObject];

  selectedObjects.forEach((obj: any) => {
    // Only apply to text objects
    if (obj.type === "textbox") {
      obj.set({ fontSize: fontSize });
    }
  });

  canvas.renderAll();
};

/*
Gets the current font size from a selected text object.
Returns undefined if object is not a text object.
*/
export const getObjectFontSize = (obj: any): number | undefined => {
  if (!obj || obj.type !== "textbox") return undefined;

  return obj.fontSize;
};

/*
Context-aware font size update based on selection state.
Selected object: changes only that object's font size.
No selection: calls onDefaultChange callback for session storage update.
*/
export const applyFontSizeContextAware = (
  canvas: Canvas,
  size: number,
  onDefaultChange?: (size: number) => void
): void => {
  const selectedObject = canvas.getActiveObject();

  if (selectedObject) {
    changeSelectedObjectsFontSize(canvas, size);
  } else {
    onDefaultChange?.(size);
  }
};
