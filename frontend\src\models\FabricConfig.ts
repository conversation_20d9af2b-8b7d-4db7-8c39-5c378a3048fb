import { CropData } from "./CropData";
import { TransformState } from "./TransformState";
import { CalibrationData } from "./CalibrationData";

export interface FabricConfig {
  contrast: number;
  brightness: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;

  annotations: Record<string, any>;
  cropData?: CropData;
  transformState?: TransformState;
  calibrationData?: CalibrationData;
}
