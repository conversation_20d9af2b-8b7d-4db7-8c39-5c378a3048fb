var _={Grayscale:1,AdobeRGB:2,RGB:3,CYMK:4},$=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),ee=4017,re=799,ne=3406,ae=2276,ie=1567,fe=3784,R=5793,se=2896;function ce(g,s){for(var e=0,P=[],h,r,c=16;c>0&&!g[c-1];)c--;P.push({children:[],index:0});var t=P[0],n;for(h=0;h<c;h++){for(r=0;r<g[h];r++){for(t=P.pop(),t.children[t.index]=s[e];t.index>0;)t=P.pop();for(t.index++,P.push(t);P.length<=h;)P.push(n={children:[],index:0}),t.children[t.index]=n.children,t=n;e++}h+1<c&&(P.push(n={children:[],index:0}),t.children[t.index]=n.children,t=n)}return P[0].children}function j(g,s,e){return 64*((g.blocksPerLine+1)*s+e)}function he(g,s,e,P,h,r,c,t,n){e.precision,e.samplesPerLine,e.scanLines;var d=e.mcusPerLine,v=e.progressive;e.maxH,e.maxV;var x=s,a=0,l=0;function i(){if(l>0)return l--,a>>l&1;if(a=g[s++],a==255){var o=g[s++];if(o)throw"unexpected marker: "+(a<<8|o).toString(16)}return l=7,a>>>7}function f(o){for(var b=o,m;(m=i())!==null;){if(b=b[m],typeof b=="number")return b;if(typeof b!="object")throw"invalid huffman sequence"}return null}function G(o){for(var b=0;o>0;){var m=i();if(m===null)return;b=b<<1|m,o--}return b}function u(o){var b=G(o);return b>=1<<o-1?b:b+(-1<<o)+1}function V(o,b){var m=f(o.huffmanTableDC),O=m===0?0:u(m);o.blockData[b]=o.pred+=O;for(var A=1;A<64;){var k=f(o.huffmanTableAC),M=k&15,q=k>>4;if(M===0){if(q<15)break;A+=16;continue}A+=q;var Q=$[A];o.blockData[b+Q]=u(M),A++}}function D(o,b){var m=f(o.huffmanTableDC),O=m===0?0:u(m)<<n;o.blockData[b]=o.pred+=O}function L(o,b){o.blockData[b]|=i()<<n}var T=0;function S(o,b){if(T>0){T--;return}for(var m=r,O=c;m<=O;){var A=f(o.huffmanTableAC),k=A&15,M=A>>4;if(k===0){if(M<15){T=G(M)+(1<<M)-1;break}m+=16;continue}m+=M;var q=$[m];o.blockData[b+q]=u(k)*(1<<n),m++}}var y=0,X;function K(o,b){for(var m=r,O=c,A=0;m<=O;){var k=$[m];switch(y){case 0:var M=f(o.huffmanTableAC),q=M&15;if(A=M>>4,q===0)A<15?(T=G(A)+(1<<A),y=4):(A=16,y=1);else{if(q!==1)throw"invalid ACn encoding";X=u(q),y=A?2:3}continue;case 1:case 2:o.blockData[b+k]?o.blockData[b+k]+=i()<<n:(A--,A===0&&(y=y==2?3:0));break;case 3:o.blockData[b+k]?o.blockData[b+k]+=i()<<n:(o.blockData[b+k]=X<<n,y=0);break;case 4:o.blockData[b+k]&&(o.blockData[b+k]+=i()<<n);break}m++}y===4&&(T--,T===0&&(y=0))}function Z(o,b,m,O,A){var k=m/d|0,M=m%d,q=k*o.v+O,Q=M*o.h+A,ve=j(o,q,Q);b(o,ve)}function E(o,b,m){var O=m/o.blocksPerLine|0,A=m%o.blocksPerLine,k=j(o,O,A);b(o,k)}var B=P.length,U,Y,w,z,I,H;v?r===0?H=t===0?D:L:H=t===0?S:K:H=V;var F=0,N,J;B==1?J=P[0].blocksPerLine*P[0].blocksPerColumn:J=d*e.mcusPerColumn,h||(h=J);for(var C,p;F<J;){for(Y=0;Y<B;Y++)P[Y].pred=0;if(T=0,B==1)for(U=P[0],I=0;I<h;I++)E(U,H,F),F++;else for(I=0;I<h;I++){for(Y=0;Y<B;Y++)for(U=P[Y],C=U.h,p=U.v,w=0;w<p;w++)for(z=0;z<C;z++)Z(U,H,F,w,z);F++}if(l=0,N=g[s]<<8|g[s+1],N<=65280)throw"marker was not found";if(N>=65488&&N<=65495)s+=2;else break}return s-x}function be(g,s,e){var P=g.quantizationTable,h,r,c,t,n,d,v,x,a,l;for(l=0;l<64;l++)e[l]=g.blockData[s+l]*P[l];for(l=0;l<8;++l){var i=8*l;if(e[1+i]===0&&e[2+i]===0&&e[3+i]===0&&e[4+i]===0&&e[5+i]===0&&e[6+i]===0&&e[7+i]===0){a=R*e[0+i]+512>>10,e[0+i]=a,e[1+i]=a,e[2+i]=a,e[3+i]=a,e[4+i]=a,e[5+i]=a,e[6+i]=a,e[7+i]=a;continue}h=R*e[0+i]+128>>8,r=R*e[4+i]+128>>8,c=e[2+i],t=e[6+i],n=se*(e[1+i]-e[7+i])+128>>8,x=se*(e[1+i]+e[7+i])+128>>8,d=e[3+i]<<4,v=e[5+i]<<4,a=h-r+1>>1,h=h+r+1>>1,r=a,a=c*fe+t*ie+128>>8,c=c*ie-t*fe+128>>8,t=a,a=n-v+1>>1,n=n+v+1>>1,v=a,a=x+d+1>>1,d=x-d+1>>1,x=a,a=h-t+1>>1,h=h+t+1>>1,t=a,a=r-c+1>>1,r=r+c+1>>1,c=a,a=n*ae+x*ne+2048>>12,n=n*ne-x*ae+2048>>12,x=a,a=d*re+v*ee+2048>>12,d=d*ee-v*re+2048>>12,v=a,e[0+i]=h+x,e[7+i]=h-x,e[1+i]=r+v,e[6+i]=r-v,e[2+i]=c+d,e[5+i]=c-d,e[3+i]=t+n,e[4+i]=t-n}for(l=0;l<8;++l){var f=l;if(e[1*8+f]===0&&e[2*8+f]===0&&e[3*8+f]===0&&e[4*8+f]===0&&e[5*8+f]===0&&e[6*8+f]===0&&e[7*8+f]===0){a=R*e[l+0]+8192>>14,e[0*8+f]=a,e[1*8+f]=a,e[2*8+f]=a,e[3*8+f]=a,e[4*8+f]=a,e[5*8+f]=a,e[6*8+f]=a,e[7*8+f]=a;continue}h=R*e[0*8+f]+2048>>12,r=R*e[4*8+f]+2048>>12,c=e[2*8+f],t=e[6*8+f],n=se*(e[1*8+f]-e[7*8+f])+2048>>12,x=se*(e[1*8+f]+e[7*8+f])+2048>>12,d=e[3*8+f],v=e[5*8+f],a=h-r+1>>1,h=h+r+1>>1,r=a,a=c*fe+t*ie+2048>>12,c=c*ie-t*fe+2048>>12,t=a,a=n-v+1>>1,n=n+v+1>>1,v=a,a=x+d+1>>1,d=x-d+1>>1,x=a,a=h-t+1>>1,h=h+t+1>>1,t=a,a=r-c+1>>1,r=r+c+1>>1,c=a,a=n*ae+x*ne+2048>>12,n=n*ne-x*ae+2048>>12,x=a,a=d*re+v*ee+2048>>12,d=d*ee-v*re+2048>>12,v=a,e[0*8+f]=h+x,e[7*8+f]=h-x,e[1*8+f]=r+v,e[6*8+f]=r-v,e[2*8+f]=c+d,e[5*8+f]=c-d,e[3*8+f]=t+n,e[4*8+f]=t-n}for(l=0;l<64;++l){var G=s+l,u=e[l];u=u<=-2056/g.bitConversion?0:u>=2024/g.bitConversion?255/g.bitConversion:u+2056/g.bitConversion>>4,g.blockData[G]=u}}function de(g,s){for(var e=s.blocksPerLine,P=s.blocksPerColumn,h=new Int32Array(64),r=0;r<P;r++)for(var c=0;c<e;c++){var t=j(s,r,c);be(s,t,h)}return s.blockData}function W(g){return g<=0?0:g>=255?255:g|0}class xe{constructor(){}load(s){var e=(function(n){this.parse(n),this.onload&&this.onload()}).bind(this);if(s.indexOf("data:")>-1){for(var P=s.indexOf("base64,")+7,h=atob(s.substring(P)),r=new Uint8Array(h.length),c=h.length-1;c>=0;c--)r[c]=h.charCodeAt(c);e(h)}else{var t=new XMLHttpRequest;t.open("GET",s,!0),t.responseType="arraybuffer",t.onload=(function(){var n=new Uint8Array(t.response);e(n)}).bind(this),t.send(null)}}parse(s){function e(){var k=s[r]<<8|s[r+1];return r+=2,k}function P(){var k=e(),M=s.subarray(r,r+k-2);return r+=M.length,M}function h(k){for(var M=Math.ceil(k.samplesPerLine/8/k.maxH),q=Math.ceil(k.scanLines/8/k.maxV),Q=0;Q<k.components.length;Q++){C=k.components[Q];var ve=Math.ceil(Math.ceil(k.samplesPerLine/8)*C.h/k.maxH),te=Math.ceil(Math.ceil(k.scanLines/8)*C.v/k.maxV),oe=M*C.h,le=q*C.v,ue=64*le*(oe+1);C.blockData=new Int16Array(ue),C.blocksPerLine=ve,C.blocksPerColumn=te}k.mcusPerLine=M,k.mcusPerColumn=q}var r=0;s.length;var c=null,t=null,n,d,v=[],x=[],a=[],l=e();if(l!=65496)throw"SOI not found";for(l=e();l!=65497;){var i,f,G;switch(l){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var u=P();l===65504&&u[0]===74&&u[1]===70&&u[2]===73&&u[3]===70&&u[4]===0&&(c={version:{major:u[5],minor:u[6]},densityUnits:u[7],xDensity:u[8]<<8|u[9],yDensity:u[10]<<8|u[11],thumbWidth:u[12],thumbHeight:u[13],thumbData:u.subarray(14,14+3*u[12]*u[13])}),l===65518&&u[0]===65&&u[1]===100&&u[2]===111&&u[3]===98&&u[4]===101&&u[5]===0&&(t={version:u[6],flags0:u[7]<<8|u[8],flags1:u[9]<<8|u[10],transformCode:u[11]});break;case 65499:for(var V=e(),D=V+r-2;r<D;){var L=s[r++],T=new Int32Array(64);if(L>>4===0)for(f=0;f<64;f++){var S=$[f];T[S]=s[r++]}else if(L>>4===1)for(f=0;f<64;f++){var y=$[f];T[y]=e()}else throw"DQT: invalid table spec";v[L&15]=T}break;case 65472:case 65473:case 65474:if(n)throw"Only single frame JPEGs supported";e(),n={},n.extended=l===65473,n.progressive=l===65474,n.precision=s[r++],n.scanLines=e(),n.samplesPerLine=e(),n.components=[],n.componentIds={};var X=s[r++],K,Z=0,E=0;for(i=0;i<X;i++){K=s[r];var B=s[r+1]>>4,U=s[r+1]&15;Z<B&&(Z=B),E<U&&(E=U);var Y=s[r+2];G=n.components.push({h:B,v:U,quantizationTable:v[Y],quantizationTableId:Y,bitConversion:255/((1<<n.precision)-1)}),n.componentIds[K]=G-1,r+=3}n.maxH=Z,n.maxV=E,h(n);break;case 65476:var w=e();for(i=2;i<w;){var z=s[r++],I=new Uint8Array(16),H=0;for(f=0;f<16;f++,r++)H+=I[f]=s[r];var F=new Uint8Array(H);for(f=0;f<H;f++,r++)F[f]=s[r];i+=17+H,(z>>4===0?a:x)[z&15]=ce(I,F)}break;case 65501:e(),d=e();break;case 65498:e();var N=s[r++],J=[],C;for(i=0;i<N;i++){var p=n.componentIds[s[r++]];C=n.components[p];var o=s[r++];C.huffmanTableDC=a[o>>4],C.huffmanTableAC=x[o&15],J.push(C)}var b=s[r++],m=s[r++],O=s[r++],A=he(s,r,n,J,d,b,m,O>>4,O&15);r+=A;break;case 65535:s[r]!==255&&r--;break;default:if(s[r-3]==255&&s[r-2]>=192&&s[r-2]<=254){r-=3;break}throw"unknown JPEG marker "+l.toString(16)}l=e()}switch(this.width=n.samplesPerLine,this.height=n.scanLines,this.jfif=c,this.adobe=t,this.components=[],n.components.length){case 1:this.colorspace=_.Grayscale;break;case 3:this.adobe?this.colorspace=_.AdobeRGB:this.colorspace=_.RGB;break;case 4:this.colorspace=_.CYMK;break;default:this.colorspace=_.Unknown}for(var i=0;i<n.components.length;i++){var C=n.components[i];!C.quantizationTable&&C.quantizationTableId!==null&&(C.quantizationTable=v[C.quantizationTableId]),this.components.push({output:de(n,C),scaleX:C.h/n.maxH,scaleY:C.v/n.maxV,blocksPerLine:C.blocksPerLine,blocksPerColumn:C.blocksPerColumn,bitConversion:C.bitConversion})}}getData16(s,e){if(this.components.length!==1)throw"Unsupported color mode";var P=this.width/s,h=this.height/e,r,c,t,n,d,v,x=0,a=this.components.length,l=s*e*a,i=new Uint16Array(l),f=new Uint16Array((this.components[0].blocksPerLine<<3)*this.components[0].blocksPerColumn*8);for(v=0;v<a;v++){r=this.components[v];for(var G=r.blocksPerLine,u=r.blocksPerColumn,V=G<<3,D,L,T=0,S=0;S<u;S++)for(var y=S<<3,X=0;X<G;X++){var K=j(r,S,X),x=0,Z=X<<3;for(D=0;D<8;D++){var T=(y+D)*V;for(L=0;L<8;L++)f[T+Z+L]=r.output[K+x++]}}c=r.scaleX*P,t=r.scaleY*h,x=v;var E,B,U;for(d=0;d<e;d++)for(n=0;n<s;n++)B=0|d*t,E=0|n*c,U=B*V+E,i[x]=f[U],x+=a}return i}getData(s,e){var P=this.width/s,h=this.height/e,r,c,t,n,d,v,x=0,a,l,i,f,G,u,V,D,L,T=this.components.length,S=s*e*T,y=new Uint8Array(S),X=new Uint8Array((this.components[0].blocksPerLine<<3)*this.components[0].blocksPerColumn*8);for(v=0;v<T;v++){r=this.components[v];for(var K=r.blocksPerLine,Z=r.blocksPerColumn,E=K<<3,B,U,Y=0,w=0;w<Z;w++)for(var z=w<<3,I=0;I<K;I++){var H=j(r,w,I),x=0,F=I<<3;for(B=0;B<8;B++){var Y=(z+B)*E;for(U=0;U<8;U++)X[Y+F+U]=r.output[H+x++]*r.bitConversion}}c=r.scaleX*P,t=r.scaleY*h,x=v;var N,J,C;for(d=0;d<e;d++)for(n=0;n<s;n++)J=0|d*t,N=0|n*c,C=J*E+N,y[x]=X[C],x+=T}switch(T){case 1:case 2:break;case 3:if(L=!0,this.adobe&&this.adobe.transformCode?L=!0:typeof this.colorTransform<"u"&&(L=!!this.colorTransform),L)for(v=0;v<S;v+=T)a=y[v],l=y[v+1],i=y[v+2],u=W(a-179.456+1.402*i),V=W(a+135.459-.344*l-.714*i),D=W(a-226.816+1.772*l),y[v]=u,y[v+1]=V,y[v+2]=D;break;case 4:if(!this.adobe)throw"Unsupported color mode (4 components)";if(L=!1,this.adobe&&this.adobe.transformCode?L=!0:typeof this.colorTransform<"u"&&(L=!!this.colorTransform),L)for(v=0;v<S;v+=T)a=y[v],l=y[v+1],i=y[v+2],f=W(434.456-a-1.402*i),G=W(119.541-a+.344*l+.714*i),a=W(481.816-a-1.772*l),y[v]=f,y[v+1]=G,y[v+2]=a;break;default:throw"Unsupported color mode"}return y}}export{xe as default};
