import { Canvas } from "fabric";
import { FabricConfig } from "./FabricConfig";
import { CropData } from "./CropData";
import { ToolMode } from "./ToolMode";

export interface ImageToolbarProps {
  fabricCanvas: React.MutableRefObject<Canvas | null>;
  fabricConfigs: FabricConfig;
  handlers: {
    filter: {
      handleBrightnessChange: (value: number) => void;
      handleContrastChange: (value: number) => void;
      handleGrayscaleChange: (value: boolean) => void;
      handleInvertChange: (value: boolean) => void;
      handleSharpnessChange: (value: number) => void;
      handleGammaRChange: (value: number) => void;
      handleGammaGChange: (value: number) => void;
      handleGammaBChange: (value: number) => void;
    };
    transform: {
      handleRotate: () => void;
      handleFlipHorizontal: () => void;
      handleFlipVertical: () => void;
    };
    actions: {
      handleUndo: () => void;
      handleSave: () => void;
      handleCrop: () => void;
    };
    tracking: {
      disableUndoTracking: () => void;
      enableUndoTracking: () => void;
    };
  };
  state: {
    canUndo: boolean;
    cropData: CropData;
  };
  config: {
    disableGrayscale?: boolean;
    disableGamma?: boolean;
  };
  onShapeCreated?: () => void;
}

export interface ActionButtonsProps {
  canUndo: boolean;
  onUndo?: () => void;
  onSave?: () => void;
  compact?: boolean;
}

export interface SliderControlsProps {
  brightness: number;
  contrast: number;
  sharpness: number;
  onBrightnessChange: (value: number) => void;
  onContrastChange: (value: number) => void;
  onSharpnessChange: (value: number) => void;
}

export interface GammaControlsProps {
  gammaR: number;
  gammaG: number;
  gammaB: number;
  disableGamma: boolean;
  onGammaRChange: (value: number) => void;
  onGammaGChange: (value: number) => void;
  onGammaBChange: (value: number) => void;
}

export interface ToolGridProps {
  activeMode: ToolMode | null;
  cropData: CropData;
  onToolSelect: (mode: ToolMode) => void;
  onCrop?: () => void;
  grayscale?: boolean;
  invert?: boolean;
  disableGrayscale?: boolean;
  onRotate?: () => void;
  onFlipHorizontal?: () => void;
  onFlipVertical?: () => void;
  onGrayscaleChange?: (value: boolean) => void;
  onInvertChange?: (value: boolean) => void;
  onOpenAdjustments?: () => void;
}
