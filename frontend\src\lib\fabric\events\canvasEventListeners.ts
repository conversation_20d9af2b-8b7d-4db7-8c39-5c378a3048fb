import { Canvas } from "fabric";
import { updateMeasurementOnModify, isMeasurementLine } from "../operations/measurements";
import { updateArrowOnModify, isArrow } from "../operations/arrows";
import { updateProtractorOnModify } from "../operations/protractor";
import { getToolConfig } from "../tools/toolConfigs";
import type {
  FabricMeasurementLine,
  FabricObjectState,
  UndoAction,
  UndoTrackingHandlers,
} from "@/models";
import { v4 as uuidv4 } from "uuid";

const isProtractor = (obj: any): boolean => {
  return obj?.name === "protractor";
};

/*
Unified annotation update handler that handles all annotation types consistently.
Each annotation type has its own helper function and update method.
*/
const updateAnnotationOnModify = (canvas: Canvas, target: any, calibrationData?: any): void => {
  if (isMeasurementLine(target)) {
    updateMeasurementOnModify(canvas, target as FabricMeasurementLine, calibrationData);
  } else if (isArrow(target)) {
    updateArrowOnModify(canvas, target);
  } else if (isProtractor(target) || target?.name?.startsWith("protractor")) {
    updateProtractorOnModify(canvas, target);
  }
};

/*
Tracks add actions for undo stack. Skips background/crop/calibration/measurement text/ arrow head.
*/
export const createUndoTrackingAddHandler = (
  undoTracking: UndoTrackingHandlers,
  canvas: Canvas
) => {
  return (e?: any) => {
    if (undoTracking.isUndoingRef.current) return;

    const addedObject = e?.path || e?.target;
    if (!addedObject) return;

    if ((addedObject as any).parentId) return;

    if (!(addedObject as any).id) {
      (addedObject as any).id = uuidv4();
    }

    const objName = (addedObject as any).name;

    if (objName === "measurementLine") {
      undoTracking.addUndoAction({
        type: "add-measurement",
        lineId: addedObject?.id,
      } as UndoAction);
      return;
    }
    if (objName === "arrow") {
      undoTracking.addUndoAction({ type: "add-arrow", lineId: addedObject?.id } as UndoAction);
      return;
    }
    if (objName === "protractor") {
      undoTracking.addUndoAction({ type: "add-protractor", lineId: addedObject?.id } as UndoAction);
      return;
    }

    if (
      objName === "backgroundImage" ||
      objName === "cropRect" ||
      objName === "measurementText" ||
      objName === "calibrateLine" ||
      objName === "arrowHead" ||
      objName === "magnifierLens" ||
      (objName?.startsWith("protractor") && objName !== "protractor")
    )
      return;

    undoTracking.addUndoAction({
      type: "add",
      objectCount: canvas.getObjects().length,
      objectId: (addedObject as any).id,
    } as UndoAction);
  };
};

/*
Tracks remove actions for undo stack (ignores calibration markers).
*/
export const createUndoTrackingRemoveHandler = (undoTracking: UndoTrackingHandlers) => {
  return (e?: any) => {
    if (!undoTracking.isUndoingRef.current) {
      const removedObject = e?.path || e?.target;
      const objName = (removedObject as any).name;
      if (objName !== "calibrateLine" && objName !== "cropRect" && objName !== "magnifierLens") {
        undoTracking.addUndoAction({
          type: "remove",
          objectCount: 0,
        });
      }
    }
  };
};

/*
On modify, records previous object state captured during move/scale/rotate for undo.
Ignores measurement text.
*/
export const createUndoTrackingModifyHandler = (
  undoTracking: UndoTrackingHandlers,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
) => {
  return (e: { target: any }) => {
    if (undoTracking.isUndoingRef.current || !e.target) return;

    const name = (e.target as any).name as string | undefined;
    if (
      name === "measurementText" ||
      name === "arrowHead" ||
      name === "magnifierLens" ||
      (name?.startsWith("protractor") && name !== "protractor")
    )
      return;

    const obj = e.target as any;
    const objId = obj.id as string;
    const previousState = objectStates.current.get(objId);
    if (previousState) {
      undoTracking.addUndoAction({ type: "modify", objectId: objId, previousState });
      objectStates.current.delete(objId);
    }
  };
};

/*
Before modifications, snapshot an object's basic transform so we can undo the change.
Skips measurement text to avoid interfering with overlays.
*/
export const createObjectStateTrackingHandler = (
  undoTracking: UndoTrackingHandlers,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
) => {
  return (e: { target: any }) => {
    if (
      undoTracking.isUndoingRef.current ||
      !e.target ||
      isMeasurementLine(e.target) ||
      (e.target as any)?.name === "measurementText" ||
      (e.target as any)?.name === "arrowHead"
    )
      return;

    const obj = e.target as any;
    let objId = obj.id;
    if (!objId) {
      objId = uuidv4();
      obj.id = objId;
    }
    if (!objectStates.current.has(objId)) {
      const base: FabricObjectState = {
        left: obj.left || 0,
        top: obj.top || 0,
        scaleX: obj.scaleX || 1,
        scaleY: obj.scaleY || 1,
        angle: obj.angle || 0,
      };
      const name = (obj as any)?.name as string | undefined;
      if (
        name === "protractor" ||
        name === "protractorRayB" ||
        name === "measurementLine" ||
        name === "arrow"
      ) {
        base.x1 = obj.x1;
        base.y1 = obj.y1;
        base.x2 = obj.x2;
        base.y2 = obj.y2;
      }

      objectStates.current.set(objId, base);
    }
  };
};

/*
Unified annotation modification handler that keeps all annotation types in sync
as objects move/scale/rotate.
*/
export const createAnnotationModificationHandler = (
  canvas: Canvas,
  trackModifyHandler: (e: { target: any }) => void,
  calibrationData?: any
) => {
  return (e: { target: any }) => {
    updateAnnotationOnModify(canvas, e.target, calibrationData);
    trackModifyHandler(e);
  };
};

/*
Unified annotation movement handler for all annotation types.
*/
export const createAnnotationMovementHandler = (canvas: Canvas, calibrationData?: any) => {
  return (e: { target: any }) => {
    updateAnnotationOnModify(canvas, e.target, calibrationData);
  };
};

/*
Registers Fabric event handlers for undo tracking, unified annotation sync
and path creation. Returns disposers from Fabric's on() registration.
*/
export const setupCanvasEventListeners = (
  canvas: Canvas,
  undoTracking: UndoTrackingHandlers,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>,
  calibrationData?: any,
  onPathCreated?: () => void,
  onObjectSelected?: (obj: any) => void
): (() => void)[] => {
  const trackAddHandler = createUndoTrackingAddHandler(undoTracking, canvas);
  const trackRemoveHandler = createUndoTrackingRemoveHandler(undoTracking);
  const trackModifyHandler = createUndoTrackingModifyHandler(undoTracking, objectStates);
  const startAnnotationHandler = createObjectStateTrackingHandler(undoTracking, objectStates);
  const modifyAnnotationHandler = createAnnotationModificationHandler(
    canvas,
    trackModifyHandler,
    calibrationData
  );
  const moveAnnotationHandler = createAnnotationMovementHandler(canvas, calibrationData);

  const handlePathCreated = (e: any) => {
    const path = e.path;
    if (!path) return;

    const freehandConfig = getToolConfig("freehand") as any;
    path.set({
      id: uuidv4(),
      perPixelTargetFind: freehandConfig.perPixelTargetFind ?? true,
    });

    // Add custom toObject override to save perPixelTargetFind property
    const originalToObject = path.toObject;
    path.toObject = function (propertiesToInclude?: any[]) {
      const pathProps = ["perPixelTargetFind"];
      const allProps = propertiesToInclude ? [...propertiesToInclude, ...pathProps] : pathProps;
      return originalToObject.call(this, allProps);
    };

    trackAddHandler(e);
    if (onPathCreated) onPathCreated();
  };

  const handleObjectSelected = (e: any) => {
    // Handle protractor handle selection - prevent multi-selection of handles
    if (e?.selected && e.selected.length > 1) {
      const handleSel = e.selected.filter(
        (o: any) => o?.name === "protractorHandle" || o?.name === "protractorRotateHandle"
      );
      if (handleSel.length > 1) {
        const last = handleSel[handleSel.length - 1];
        canvas.discardActiveObject();
        canvas.setActiveObject(last);
        canvas.requestRenderAll();
        return;
      }
    }

    if (onObjectSelected && e.target) {
      const objName = (e.target as any)?.name;
      if (
        objName !== "backgroundImage" &&
        objName !== "measurementText" &&
        objName !== "arrowHead"
      ) {
        onObjectSelected(e.target);
      }
    }
  };

  const disposers: (() => void)[] = [];

  disposers.push(canvas.on("path:created", handlePathCreated));

  disposers.push(
    canvas.on("object:added", (e) => {
      if (e.target && e.target.type !== "path") {
        trackAddHandler(e);
      }
    })
  );

  disposers.push(canvas.on("object:removed", trackRemoveHandler));

  disposers.push(
    canvas.on("object:moving", (e) => {
      startAnnotationHandler(e);
      moveAnnotationHandler(e);
      if (isArrow(e.target)) updateArrowOnModify(canvas, e.target);
    })
  );
  disposers.push(
    canvas.on("object:scaling", (e) => {
      startAnnotationHandler(e);
      moveAnnotationHandler(e);
    })
  );
  disposers.push(
    canvas.on("object:rotating", (e) => {
      startAnnotationHandler(e);
      moveAnnotationHandler(e);
    })
  );
  disposers.push(canvas.on("object:modified", modifyAnnotationHandler));

  disposers.push(canvas.on("selection:created", handleObjectSelected));
  disposers.push(canvas.on("selection:updated", handleObjectSelected));

  return disposers;
};
