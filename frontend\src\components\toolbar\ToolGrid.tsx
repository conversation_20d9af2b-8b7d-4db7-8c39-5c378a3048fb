import React from "react";
import { Button } from "@blueprintjs/core";
import type { ToolDefinition, ToolGridProps } from "@/models";
import { toolDefinitions } from "@/lib/fabric/tools";
import type { IconName } from "@blueprintjs/icons";

/*
Handles different tool types (mode, action, toggle) consistently.
*/
const createToolClickHandler = (
  onToolSelect: (mode: any) => void,
  onCrop: (() => void) | undefined,
  onRotate: (() => void) | undefined,
  onFlipHorizontal: (() => void) | undefined,
  onFlipVertical: (() => void) | undefined,
  onGrayscaleChange: ((value: boolean) => void) | undefined,
  onInvertChange: ((value: boolean) => void) | undefined,

  cropData: any,
  grayscale: boolean,
  invert: boolean,
  disableGrayscale: boolean
) => {
  return async (tool: ToolDefinition) => {
    if (tool.type === "mode") {
      if (tool.mode === "crop") {
        if (cropData.isCropped) {
          onCrop?.();
          onToolSelect("select");
        } else {
          onToolSelect("crop");
        }
        return;
      }
      if (tool.mode) onToolSelect(tool.mode);
    } else if (tool.type === "action") {
      switch (tool.action) {
        case "rotate":
          onRotate?.();
          break;
        case "flipHorizontal":
          onFlipHorizontal?.();
          break;
        case "flipVertical":
          onFlipVertical?.();
          break;
      }
    } else if (tool.type === "toggle") {
      switch (tool.action) {
        case "grayscale":
          if (!disableGrayscale) {
            onGrayscaleChange?.(!grayscale);
          }
          break;
        case "invert":
          onInvertChange?.(!invert);
          break;
      }
    }
  };
};

const ToolGrid: React.FC<ToolGridProps> = ({
  activeMode,
  cropData,
  onToolSelect,
  onCrop,
  grayscale,
  invert,
  disableGrayscale,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onGrayscaleChange,
  onInvertChange,
  onOpenAdjustments,
}) => {
  const handleToolClick = createToolClickHandler(
    onToolSelect,
    onCrop,
    onRotate,
    onFlipHorizontal,
    onFlipVertical,
    onGrayscaleChange,
    onInvertChange,
    cropData,
    grayscale || false,
    invert || false,
    disableGrayscale || false
  );

  const getBpIcon = (tool: ToolDefinition): IconName => {
    if (tool.type === "mode") {
      switch (tool.mode) {
        case "select":
          return "hand" as IconName;
        case "freehand":
          return "draw" as IconName;
        case "text":
          return "new-text-box" as IconName;
        case "rect":
          return "rectangle" as IconName;
        case "highlight":
          return "highlight" as IconName;
        case "line":
          return "minus" as IconName;
        case "circle":
          return "full-circle" as IconName;
        case "crop":
          return "crop" as IconName;
        case "measure":
          return "drag-handle-horizontal" as IconName;
        case "calibrate":
          return "compass" as IconName;
        case "arrow":
          return "arrow-right" as IconName;
        case "magnifier":
          return "zoom-in" as IconName;
        case "protractor":
          return "ring" as IconName;
        default:
          return "dot" as IconName;
      }
    }
    if (tool.type === "action") {
      switch (tool.action) {
        case "rotate":
          return "refresh" as IconName;
        case "flipHorizontal":
          return "arrows-horizontal" as IconName;
        case "flipVertical":
          return "arrows-vertical" as IconName;
        default:
          return "cog" as IconName;
      }
    }
    if (tool.type === "toggle") {
      switch (tool.action) {
        case "grayscale":
          return "filter" as IconName;
        case "invert":
          return "exchange" as IconName;
        default:
          return "flash" as IconName;
      }
    }
    return "dot" as IconName;
  };

  return (
    <div style={{ display: "grid", gridTemplateColumns: "repeat(3, 28px)", gap: 4 }}>
      <Button
        className="bp5-small bp5-minimal"
        key="adjustments"
        onClick={onOpenAdjustments}
        title="Adjustments"
        icon={"cog" as IconName}
        style={{ width: 28, height: 28, padding: 0 }}
      />
      {toolDefinitions.map((tool, index) => {
        let isActive = false;
        let isDisabled = false;
        let buttonTitle = tool.title;
        if (tool.type === "mode") {
          isActive = activeMode === tool.mode;
          if (tool.mode === "crop" && cropData.isCropped) buttonTitle = "Restore original image";
        } else if (tool.type === "toggle") {
          if (tool.action === "grayscale") {
            isActive = grayscale || false;
            isDisabled = disableGrayscale || false;
          } else if (tool.action === "invert") {
            isActive = invert || false;
          }
        }
        return (
          <Button
            className="bp5-small bp5-minimal"
            key={tool.mode || tool.action || index}
            onClick={() => handleToolClick(tool)}
            disabled={isDisabled}
            title={buttonTitle}
            active={isActive}
            intent={isActive ? "primary" : "none"}
            icon={getBpIcon(tool)}
            style={{ width: 28, height: 28, padding: 0 }}
          />
        );
      })}
    </div>
  );
};

export default ToolGrid;
