import React from "react";
import { Button, ButtonGroup } from "@blueprintjs/core";
import type { ActionButtonsProps } from "@/models";

const ActionButtons: React.FC<ActionButtonsProps> = ({ canUndo, onUndo, onSave, compact }) => {
  if (compact) {
    return (
      <div style={{ display: "flex", gap: 4 }}>
        {onUndo && (
          <Button
            className="bp5-small bp5-minimal"
            icon="undo"
            disabled={!canUndo}
            onClick={onUndo}
            title="Undo"
            style={{ width: 28, height: 28, padding: 0 }}
          />
        )}
        <Button
          className="bp5-small bp5-minimal"
          icon="floppy-disk"
          intent="primary"
          onClick={onSave}
          title="Save"
          style={{ width: 28, height: 28, padding: 0 }}
        />
      </div>
    );
  }
  return (
    <ButtonGroup fill>
      {onUndo && <Button icon="undo" disabled={!canUndo} onClick={onUndo} title="Undo" />}
      <Button icon="floppy-disk" intent="primary" onClick={onSave} title="Save" />
    </ButtonGroup>
  );
};

export default ActionButtons;
