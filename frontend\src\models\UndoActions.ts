export type UndoActionType =
  | "add"
  | "remove"
  | "modify"
  | "filter"
  | "rotate"
  | "flipH"
  | "flipV"
  | "crop";

export interface FabricObjectState {
  left: number;
  top: number;
  scaleX: number;
  scaleY: number;
  angle: number;
  x1?: number;
  y1?: number;
  x2?: number;
  y2?: number;
}

export interface AddUndoAction {
  type: "add";
  objectCount: number;
}

export interface RemoveUndoAction {
  type: "remove";
  objectCount: number;
}

export interface ModifyUndoAction {
  type: "modify";
  objectId: string;
  previousState: FabricObjectState;
}

export interface MeasurementUndoAction {
  type: "add-measurement";
  lineId: string;
}

export interface ArrowUndoAction {
  type: "add-arrow";
  lineId: string;
}

export interface ProtractorUndoAction {
  type: "add-protractor";
  lineId: string;
}

export type UndoAction =
  | AddUndoAction
  | RemoveUndoAction
  | ModifyUndoAction
  | MeasurementUndoAction
  | ArrowUndoAction
  | ProtractorUndoAction;

export interface UndoTrackingHandlers {
  isUndoingRef: React.MutableRefObject<boolean>;
  addUndoAction: (action: UndoAction) => void;
}

export interface UndoTrackingState {
  undoStack: UndoAction[];
  canUndo: boolean;
  isUndoingRef: React.MutableRefObject<boolean>;
  handleUndo: () => Promise<void>;
  addUndoAction: (action: UndoAction) => void;
  disableUndoTracking: () => void;
  enableUndoTracking: () => void;
}
