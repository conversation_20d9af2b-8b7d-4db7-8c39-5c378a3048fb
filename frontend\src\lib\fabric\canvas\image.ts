import { Canvas } from "fabric";
import type { LoadImageOptions } from "@/models";

/** Loads background image, fits to container with aspect ratio, and sets canvas size */

export const loadCanvasImage = async (
  canvas: Canvas,
  imageUrl: string,
  options?: LoadImageOptions
): Promise<{ scale: number; width: number; height: number }> => {
  const { FabricImage } = await import("fabric");
  const backgroundImage = await FabricImage.fromURL(imageUrl, { crossOrigin: "anonymous" });
  backgroundImage.set({
    left: 0,
    top: 0,
    selectable: false,
    evented: false,
    name: "backgroundImage",
  });

  let width: number, height: number, scale: number;

  if (options?.containerRect) {
    const aspect = (backgroundImage.width || 512) / (backgroundImage.height || 512);
    let newWidth = options.containerRect.width;
    let newHeight = newWidth / aspect;
    if (newHeight > options.containerRect.height) {
      newHeight = options.containerRect.height;
      newWidth = newHeight * aspect;
    }
    scale = newWidth / (backgroundImage.width || 512);
    width = newWidth;
    height = newHeight;
  } else {
    scale = window.innerHeight / (backgroundImage.height || 512);
    width = Math.round((backgroundImage.width || 512) * scale);
    height = Math.round((backgroundImage.height || 512) * scale);
  }

  canvas.setDimensions({ width, height });
  backgroundImage.set({ scaleX: scale, scaleY: scale });
  canvas.backgroundImage = backgroundImage;
  canvas.renderAll();

  return { scale, width, height };
};
