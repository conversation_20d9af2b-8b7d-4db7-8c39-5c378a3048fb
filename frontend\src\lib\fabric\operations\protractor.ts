import { Canvas, Line, Circle, FabricText } from "fabric";
import { v4 as uuidv4 } from "uuid";
import { getObjectConfig } from "../tools/toolConfigs";

const toAngle = (dx: number, dy: number) => Math.atan2(dy, dx);
const norm = (a: number) => ((a % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);
const dist = (x1: number, y1: number, x2: number, y2: number) => Math.hypot(x2 - x1, y2 - y1);

const labelPos = (cx: number, cy: number, r: number, aMid: number) => ({
  x: cx + (r + 18) * Math.cos(aMid),
  y: cy + (r + 18) * Math.sin(aMid),
});

const getById = (canvas: Canvas, id?: string) =>
  canvas.getObjects().find((o) => (o as any).id === id) as any;

export const getProtractorFromAny = (canvas: Canvas, obj: any): any | undefined => {
  if (!obj) return;
  const name = (obj as any).name as string;
  if (name === "protractor") return obj as any;
  const parentId = (obj as any).parentId as string;
  if (!parentId) return;
  const protractor = getById(canvas, parentId);
  return protractor?.name === "protractor" ? (protractor as any) : undefined;
};

const handleRadius = 3;
const movementThreshold = 0.5;

const setObjectProps = (obj: any, props: any) => {
  obj.set(props);
  obj.setCoords();
};

const updateHandlePositions = (canvas: Canvas, protractor: any, rayB: Line) => {
  const rayA = protractor;
  const vertex = rayA.vertex;
  const handleA = getById(canvas, rayA.handleAId) as any;
  const handleB = getById(canvas, rayA.handleBId) as any;
  const vertexHandle = getById(canvas, rayA.vertexHandleId) as any;
  const rotateHandle = getById(canvas, rayA.rotateHandleId) as any;

  if (handleA) {
    const handleRadius = handleA.radius || 3;
    const strokeWidth = rayA.strokeWidth || 2;
    const rayALength = Math.hypot(rayA.x2! - vertex.x, rayA.y2! - vertex.y);
    if (rayALength > 0) {
      const dirX = (rayA.x2! - vertex.x) / rayALength;
      const dirY = (rayA.y2! - vertex.y) / rayALength;
      const offset = strokeWidth / 2 + handleRadius;
      const handleX = rayA.x2! + offset * dirX;
      const handleY = rayA.y2! + offset * dirY;
      setObjectProps(handleA, { left: handleX, top: handleY });
    }
  }

  if (handleB) {
    const handleRadius = handleB.radius || 3;
    const strokeWidth = rayB.strokeWidth || rayA.strokeWidth || 2;
    const rayBLength = Math.hypot(rayB.x2! - vertex.x, rayB.y2! - vertex.y);
    if (rayBLength > 0) {
      const dirX = (rayB.x2! - vertex.x) / rayBLength;
      const dirY = (rayB.y2! - vertex.y) / rayBLength;
      const offset = strokeWidth / 2 + handleRadius;
      const handleX = rayB.x2! + offset * dirX;
      const handleY = rayB.y2! + offset * dirY;
      setObjectProps(handleB, { left: handleX, top: handleY });
    }
  }

  if (vertexHandle) {
    setObjectProps(vertexHandle, { left: vertex.x, top: vertex.y });
  }

  if (rotateHandle) {
    setObjectProps(rotateHandle, { left: vertex.x, top: vertex.y });
  }
};

const makeHandle = (
  x: number,
  y: number,
  parentId: string,
  handleType: "rayA" | "rayB" | "vertex"
) => {
  const handleConfig = getObjectConfig("protractorHandle");
  const handle = new Circle({
    left: x,
    top: y,
    radius: handleRadius,
    ...handleConfig,
    name: "protractorHandle",
  }) as any;

  handle.id = uuidv4();
  handle.parentId = parentId;
  handle.handleOf = handleType;
  return handle;
};

const makeRotateHandle = (x: number, y: number, parentId: string) => {
  const rotateHandleConfig = getObjectConfig("protractorRotateHandle");
  const rotateHandle = new Circle({
    left: x,
    top: y,
    radius: handleRadius,
    ...rotateHandleConfig,
    name: "protractorRotateHandle",
  }) as any;

  rotateHandle.id = uuidv4();
  rotateHandle.parentId = parentId;
  (rotateHandle as any).handleOf = "rotate";
  (rotateHandle as any).lockScalingX = true;
  (rotateHandle as any).lockScalingY = true;
  (rotateHandle as any).lockUniScaling = true;

  return rotateHandle;
};

/*
Creates protractor annotation with two rays and angle measurement.
Uses multiple connected objects: main ray, second ray, handles, and text label.
Supports two-phase drawing: first ray, then second ray for angle measurement.
*/
export const createProtractor = (
  canvas: Canvas,
  start: { x: number; y: number },
  config: any
): any => {
  const id = uuidv4();

  const rayA = new Line([start.x, start.y, start.x, start.y], {
    stroke: config.stroke,
    ...config,
  }) as any;
  rayA.id = id;
  rayA.vertex = { x: start.x, y: start.y };
  rayA.phase = 1;
  rayA.arcRadius = config.arcRadius || 32;
  rayA.handleRadius = config.handleRadius || 3;
  const rayB = new Line([start.x, start.y, start.x, start.y], {
    stroke: config.stroke,
    strokeWidth: config.strokeWidth,
    strokeUniform: config.strokeUniform,
    strokeLineCap: config.strokeLineCap,
    strokeLineJoin: config.strokeLineJoin,
    selectable: config.selectable,
    evented: config.evented,
    hasControls: config.hasControls,
    hasBorders: config.hasBorders,
    perPixelTargetFind: config.perPixelTargetFind,
    padding: 0,
    hoverCursor: config.hoverCursor,
    name: "protractorLine",
    objectCaching: config.objectCaching,
  }) as any;

  rayB.id = uuidv4();
  rayB.parentId = id;

  const angleLabel = new FabricText("0°", {
    fontSize: 18,
    fill: config.stroke,
    backgroundColor: "rgba(255,255,255,0.8)",
    selectable: false,
    evented: false,
    originX: "center",
    originY: "center",
    name: "protractorLabel",
    objectCaching: false,
    visible: false,
  }) as any;
  (angleLabel as any).id = uuidv4();
  angleLabel.parentId = id;

  const handleA = makeHandle(start.x, start.y, id, "rayA");
  const handleB = makeHandle(start.x, start.y, id, "rayB");
  const vertexHandle = makeHandle(start.x, start.y, id, "vertex");

  const rotateHandle = makeRotateHandle(start.x, start.y, id);

  canvas.add(rayB);
  canvas.add(handleA);
  canvas.add(handleB);
  canvas.add(vertexHandle);
  canvas.add(rotateHandle);
  canvas.add(angleLabel);

  canvas.add(rayA);

  rayA.rayBId = rayB.id;
  rayA.textId = (angleLabel as any).id;
  rayA.handleAId = handleA.id;
  rayA.handleBId = handleB.id;
  rayA.vertexHandleId = vertexHandle.id;
  rayA.rotateHandleId = rotateHandle.id;

  rayA.__selfAdded = true;

  return rayA;
};

/*
Updates protractor geometry during drag by setting ray coordinates.
Calculates angle between rays and updates text label with degree measurement.
Handles both phase 1 (first ray) and phase 2 (second ray) drawing.
*/
export const updateProtractorSize = (
  protractor: any,
  currentPoint: { x: number; y: number },
  canvas: Canvas
) => {
  const rayA = protractor; // protractor object IS the first ray (Ray A)
  const vertex = rayA.vertex;
  const rayB = getById(canvas, rayA.rayBId) as Line | undefined;
  const angleText = getById(canvas, rayA.textId) as FabricText | undefined;

  if (!rayB || !angleText) return;

  if (rayA.phase === 1) {
    rayA.set({ x1: vertex.x, y1: vertex.y, x2: currentPoint.x, y2: currentPoint.y });
    rayA.setCoords();
  } else {
    rayB.set({ x1: vertex.x, y1: vertex.y, x2: currentPoint.x, y2: currentPoint.y });
    rayB.setCoords();
  }

  const rayAReady = dist(rayA.x1!, rayA.y1!, rayA.x2!, rayA.y2!) > 0.5;
  const rayBReady = dist(rayB.x1!, rayB.y1!, rayB.x2!, rayB.y2!) > 0.5;

  if (rayAReady && rayBReady) {
    const angleA = toAngle(rayA.x2! - vertex.x, rayA.y2! - vertex.y);
    const angleB = toAngle(rayB.x2! - vertex.x, rayB.y2! - vertex.y);

    let startAngle = norm(angleA);
    let endAngle = norm(angleB);
    const angleDiff = endAngle - startAngle;
    if (angleDiff > Math.PI) startAngle += 2 * Math.PI;
    if (angleDiff < -Math.PI) endAngle += 2 * Math.PI;

    const totalAngle = Math.abs(endAngle - startAngle);

    const arcRadius = rayA.arcRadius ?? 32;

    const midAngle = (startAngle + endAngle) / 2;
    const labelPosition = labelPos(vertex.x, vertex.y, arcRadius, midAngle);
    angleText.set({
      text: `${(totalAngle * (180 / Math.PI)).toFixed(1)}°`,
      left: labelPosition.x,
      top: labelPosition.y,
      visible: true,
    });
  } else {
    angleText.set({ visible: false });
  }

  updateHandlePositions(canvas, rayA, rayB);

  canvas.requestRenderAll();
};

/*
Advances protractor from phase 1 (first ray) to phase 2 (second ray).
Returns true when protractor is complete, false when advancing to next phase.
*/
export const advanceProtractorPhase = (protractor: any): boolean => {
  if (protractor.phase === 1) {
    protractor.phase = 2;
    return false;
  }
  return true;
};

/*
Makes protractor and handles selectable/evented after creation.
Configures interaction settings for rays, handles, and rotation controls.
*/
export const finalizeProtractor = (canvas: Canvas, protractor: any) => {
  const rayB = getById(canvas, protractor.rayBId) as Line | undefined;
  const text = getById(canvas, protractor.textId) as any;
  const hA = getById(canvas, protractor.handleAId) as any;
  const hB = getById(canvas, protractor.handleBId) as any;
  const hV = getById(canvas, protractor.vertexHandleId) as any;
  const hR = getById(canvas, protractor.rotateHandleId) as any;

  [protractor, rayB, text].forEach((o: any) => {
    if (!o) return;
    o.selectable = true;
    o.evented = true;
    o.hasControls = false;
    o.hasBorders = false;
    o.lockMovementX = false;
    o.lockMovementY = false;
    o.perPixelTargetFind = false;
    o.padding = 0;
    o.strokeLineCap = "butt";
    if ((o as any).name === "protractor" || (o as any).name === "protractorLine") {
      (o as any).hoverCursor = "move";
      (o as any).moveCursor = "move";
    }
  });

  [hA, hB, hV].forEach((h: any) => {
    if (!h) return;
    h.selectable = true;
    h.evented = true;
    h.excludeFromActiveGroup = true;
    h.hasControls = false;
    h.hasBorders = false;
    h.hoverCursor = "crosshair";
    h.moveCursor = "crosshair";
    h.perPixelTargetFind = false;
    h.padding = 0;
  });

  if (hR) {
    hR.selectable = true;
    hR.evented = true;
    hR.excludeFromActiveGroup = true;

    if (typeof (hR as any).setControlsVisibility === "function") {
      (hR as any).setControlsVisibility({
        tl: false,
        tr: false,
        bl: false,
        br: false,
        ml: false,
        mt: false,
        mr: false,
        mb: false,
        mtr: true,
      });
    }

    hR.hasControls = true;
    hR.hasBorders = false;
    hR.lockScalingX = true;
    hR.lockScalingY = true;
    hR.lockUniScaling = true;
    hR.hoverCursor = "crosshair";
    hR.moveCursor = "crosshair";
    hR.perPixelTargetFind = false;
    hR.padding = 0;

    hR.set({ left: protractor.vertex.x, top: protractor.vertex.y });
    hR.setCoords();
  }

  canvas.requestRenderAll();
};

/*
Central handler for protractor modifications.
Handles ray moves, handle drags, vertex moves, and rotation operations.
Updates all dependent geometry, labels, and handle positions.
*/
export const updateProtractorOnModify = (canvas: Canvas, target: any, ev?: any) => {
  const rayA = getProtractorFromAny(canvas, target);
  if (!rayA) return;

  const rayB = getById(canvas, rayA.rayBId) as Line | undefined;
  if (!rayB) return;

  const targetName = (target as any)?.name as string | undefined;
  const handleType = (target as any)?.handleOf as string | undefined;

  const isHandle =
    targetName === "protractorHandle" ||
    targetName === "protractorRotateHandle" ||
    handleType === "rotate";
  if (isHandle) {
    const handle = target as any;

    const isRotateHandle = targetName === "protractorRotateHandle" || handleType === "rotate";
    if (isRotateHandle) {
      const vertex = rayA.vertex;

      if (typeof handle.__lastLeft !== "number") handle.__lastLeft = handle.left ?? vertex.x;
      if (typeof handle.__lastTop !== "number") handle.__lastTop = handle.top ?? vertex.y;

      const leftDelta = Math.abs((handle.left ?? 0) - (handle.__lastLeft ?? 0));
      const topDelta = Math.abs((handle.top ?? 0) - (handle.__lastTop ?? 0));

      if (leftDelta > movementThreshold || topDelta > movementThreshold) {
        const newVertex = { x: handle.left!, y: handle.top! };
        rayA.vertex = newVertex;

        setObjectProps(rayA, { x1: newVertex.x, y1: newVertex.y });
        setObjectProps(rayB, { x1: newVertex.x, y1: newVertex.y });

        updateProtractorSize(rayA, { x: rayB.x2!, y: rayB.y2! }, canvas);
        handle.__lastLeft = handle.left;
        handle.__lastTop = handle.top;
        if (typeof handle.__lastAngle !== "undefined") delete handle.__lastAngle;
        canvas.requestRenderAll();
        return;
      }

      const currentDegrees = (handle as any).angle ?? 0;
      if (typeof handle.__lastAngle !== "number") {
        handle.__lastAngle = currentDegrees;
        return;
      }

      let deltaAngle = currentDegrees - handle.__lastAngle;
      if (deltaAngle > 180) deltaAngle -= 360;
      if (deltaAngle < -180) deltaAngle += 360;
      const deltaRadians = (deltaAngle * Math.PI) / 180;

      if (Math.abs(deltaRadians) > 1e-8) {
        const rotatePoint = (px: number, py: number, vx: number, vy: number, d: number) => {
          const dx = px - vx;
          const dy = py - vy;
          const r = Math.hypot(dx, dy);
          const a = Math.atan2(dy, dx) + d;
          return { x: vx + r * Math.cos(a), y: vy + r * Math.sin(a) };
        };

        const newRayAEnd = rotatePoint(
          rayA.x2 ?? rayA.x1 ?? vertex.x,
          rayA.y2 ?? rayA.y1 ?? vertex.y,
          vertex.x,
          vertex.y,
          deltaRadians
        );
        const newRayBEnd = rotatePoint(
          rayB.x2 ?? rayB.x1 ?? vertex.x,
          rayB.y2 ?? rayB.y1 ?? vertex.y,
          vertex.x,
          vertex.y,
          deltaRadians
        );

        setObjectProps(rayA, { x1: vertex.x, y1: vertex.y, x2: newRayAEnd.x, y2: newRayAEnd.y });
        setObjectProps(rayB, { x1: vertex.x, y1: vertex.y, x2: newRayBEnd.x, y2: newRayBEnd.y });

        updateProtractorSize(rayA, { x: rayB.x2!, y: rayB.y2! }, canvas);
      }

      handle.__lastAngle = currentDegrees;
      setObjectProps(handle, { left: vertex.x, top: vertex.y });
      canvas.requestRenderAll();
      return;
    }

    if (handle.handleOf === "vertex") {
      const newVertex = { x: handle.left!, y: handle.top! };
      rayA.vertex = newVertex;
      setObjectProps(rayA, { x1: newVertex.x, y1: newVertex.y });
      setObjectProps(rayB, { x1: newVertex.x, y1: newVertex.y });

      updateProtractorSize(rayA, { x: rayB.x2!, y: rayB.y2! }, canvas);
      return;
    }

    const vertex = rayA.vertex;
    setObjectProps(rayA, { x1: vertex.x, y1: vertex.y });
    setObjectProps(rayB, { x1: vertex.x, y1: vertex.y });

    if (handle.handleOf === "rayA") {
      setObjectProps(rayA, { x2: handle.left!, y2: handle.top! });
    } else {
      setObjectProps(rayB, { x2: handle.left!, y2: handle.top! });
    }

    updateProtractorSize(rayA, { x: rayB.x2!, y: rayB.y2! }, canvas);

    if (typeof handle.__lastAngle !== "undefined") delete (handle as any).__lastAngle;
    return;
  }

  const name = (target as any).name as string | undefined;
  if (name === "protractor" || name?.startsWith("protractor")) {
    const t: any = target;

    let dx = 0;
    let dy = 0;
    if (ev && ev.pointer && typeof ev.pointer.x === "number" && typeof ev.pointer.y === "number") {
      if (typeof t.__lastPointerX !== "number" || typeof t.__lastPointerY !== "number") {
        t.__lastPointerX = ev.pointer.x;
        t.__lastPointerY = ev.pointer.y;
        return;
      }
      dx = ev.pointer.x - t.__lastPointerX;
      dy = ev.pointer.y - t.__lastPointerY;

      t.__lastPointerX = ev.pointer.x;
      t.__lastPointerY = ev.pointer.y;
    } else {
      const lastLeft = typeof t.__lastLeft === "number" ? t.__lastLeft : t.left ?? 0;
      const lastTop = typeof t.__lastTop === "number" ? t.__lastTop : t.top ?? 0;
      const currLeft = t.left ?? 0;
      const currTop = t.top ?? 0;

      dx = currLeft - lastLeft;
      dy = currTop - lastTop;

      if (Math.abs(dx) < 1e-8 && Math.abs(dy) < 1e-8) {
        t.__lastLeft = currLeft;
        t.__lastTop = currTop;
        return;
      }

      t.__lastLeft = currLeft;
      t.__lastTop = currTop;
    }

    setObjectProps(rayA, {
      x1: (rayA.x1 ?? 0) + dx,
      y1: (rayA.y1 ?? 0) + dy,
      x2: (rayA.x2 ?? 0) + dx,
      y2: (rayA.y2 ?? 0) + dy,
    });

    if (rayB) {
      setObjectProps(rayB, {
        x1: (rayB.x1 ?? 0) + dx,
        y1: (rayB.y1 ?? 0) + dy,
        x2: (rayB.x2 ?? 0) + dx,
        y2: (rayB.y2 ?? 0) + dy,
      });
    }

    rayA.vertex = { x: rayA.vertex.x + dx, y: rayA.vertex.y + dy };

    updateProtractorSize(rayA, { x: rayB.x2!, y: rayB.y2! }, canvas);

    canvas.requestRenderAll();
    return;
  }
};

export const isProtractor = (obj: any): obj is any => {
  return obj?.name === "protractor";
};

/*
Updates color for all protractor components (rayA, rayB, text).
Ensures consistent color across the entire protractor annotation.
*/
export const updateProtractorColor = (canvas: Canvas, protractor: any, color: string): void => {
  if (!protractor || protractor.name !== "protractor") return;

  const rayA = protractor; // protractor object IS the first ray (Ray A)
  rayA.set({ stroke: color });

  const rayB = getById(canvas, rayA.rayBId) as Line | undefined;
  if (rayB) {
    rayB.set({ stroke: color });
  }

  const angleText = getById(canvas, rayA.textId) as FabricText | undefined;
  if (angleText) {
    angleText.set({ fill: color });
  }

  canvas.renderAll();
};
