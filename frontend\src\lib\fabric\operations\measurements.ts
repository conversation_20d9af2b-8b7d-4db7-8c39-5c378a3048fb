import { Canvas, Line } from "fabric";
import type { CalibrationData } from "@/models";
import { getCalibrationFromLocalStorage } from "./index";
import { v4 as uuidv4 } from "uuid";
/*
Converts pixel distance to mm using calibration data and current image scale.
Accounts for scale differences between calibration time and current display.
*/
const getCalibratedDistanceInMM = (
  pixelLength: number,
  canvas: Canvas,
  calibrationData: CalibrationData
): number => {
  const { calibrationDistance, calibratedPixelLength, calibrationImageScale } = calibrationData;
  const currentScale = canvas.backgroundImage?.scaleX ?? 1;

  const scaleRatio = currentScale / calibrationImageScale;
  const correctedPx = pixelLength / scaleRatio;

  return (calibrationDistance / calibratedPixelLength) * correctedPx;
};

export const isCalibrated = (calibrationData?: CalibrationData): boolean => {
  return !!(calibrationData && calibrationData.calibrationDistance > 0);
};

/*
Returns a function that checks calibration before measuring and optionally opens
calibration UI if missing.
*/
export const createMeasurementCheckHandler = (
  calibrationData?: CalibrationData,
  onCalibrationPrompt?: () => void
) => {
  return (): boolean => {
    const localStorageData = getCalibrationFromLocalStorage();
    const finalCalibrationData =
      localStorageData && isCalibrated(localStorageData) ? localStorageData : calibrationData;

    if (!isCalibrated(finalCalibrationData)) {
      onCalibrationPrompt?.();
      return false;
    }
    return true;
  };
};

/*
Creates measurement line as single Line object with custom text rendering.
Uses custom _render method to draw distance text at tip with calibrated values.
Avoids Fabric.js Group selection issues by using single selectable object.
*/
export const createMeasurementLine = (
  start: { x: number; y: number },
  config: any
): Line & { id: string; measurementText?: string } => {
  const line = new Line([start.x, start.y, start.x, start.y], {
    stroke: config.stroke,
    ...config,
  });

  (line as any).id = uuidv4();

  (line as any).measurementText = "0.00 mm";
  (line as any).customType = "measurementLine";
  const originalToObject = line.toObject;
  (line as any).toObject = function (propertiesToInclude?: any[]) {
    const customProps = ["measurementText", "customType", "id"];
    const allProps = propertiesToInclude ? [...propertiesToInclude, ...customProps] : customProps;
    return originalToObject.call(this, allProps as any);
  };

  const originalRender = line._render;
  (line as any)._render = function (ctx: CanvasRenderingContext2D) {
    originalRender.call(this, ctx);

    const text = (this as any).measurementText || "0.00 mm";
    const points = this.calcLinePoints();
    const x1 = points.x1;
    const y1 = points.y1;
    const x2 = points.x2;
    const y2 = points.y2;

    if (Math.abs(x2 - x1) < 1 && Math.abs(y2 - y1) < 1) return;

    const tipX = x1;
    const tipY = y1;

    ctx.save();
    ctx.translate(tipX, tipY);

    const baseSize = 20;
    const scaleX = this.scaleX || 1;
    const scaleY = this.scaleY || 1;

    ctx.scale(1 / scaleX, 1 / scaleY);

    ctx.fillStyle = this.stroke || "#ff0000";
    ctx.font = `${baseSize}px Arial`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    const dy = y2 - y1;
    const textDistance = 15;
    const offsetY = dy >= 0 ? -textDistance : textDistance;

    const textWidth = ctx.measureText(text).width;
    const bgPadding = 4;
    ctx.fillStyle = "rgba(255, 255, 255, 0.9)";
    ctx.fillRect(
      -textWidth / 2 - bgPadding,
      offsetY - baseSize / 2 - bgPadding,
      textWidth + bgPadding * 2,
      baseSize + bgPadding * 2
    );

    ctx.fillStyle = this.stroke || "#ff0000";
    ctx.fillText(text, 0, offsetY);
    ctx.restore();
  };

  return line as Line & { id: string; measurementText?: string };
};

/*
Updates measurement geometry during drag by setting line coordinates.
Recalculates distance text using calibration data if available.
*/
export const updateMeasurementSize = (
  line: Line,
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number }
): void => {
  line.set({
    x1: startPoint.x,
    y1: startPoint.y,
    x2: currentPoint.x,
    y2: currentPoint.y,
  });

  line.setCoords();
  line.canvas?.requestRenderAll();
};

/*
Updates measurement text with calibrated distance in mm.
Accounts for object scaling when calculating real distance from pixel measurements.
*/
export const updateMeasurementText = (
  canvas: Canvas,
  line: Line,
  calibrationData: CalibrationData
): void => {
  const x1 = (line.x1 || 0) * (line.scaleX || 1);
  const y1 = (line.y1 || 0) * (line.scaleY || 1);
  const x2 = (line.x2 || 0) * (line.scaleX || 1);
  const y2 = (line.y2 || 0) * (line.scaleY || 1);
  const realDistance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
  const distanceMM = getCalibratedDistanceInMM(realDistance, canvas, calibrationData);
  (line as any).measurementText = `${distanceMM.toFixed(2)} mm`;
  canvas.renderAll();
};

/*
Updates measurement text after line modification (move/scale/rotate).
Uses calibration if available, otherwise shows pixel distance.
*/
export const updateMeasurementOnModify = (
  canvas: Canvas,
  line: Line,
  calibrationData?: CalibrationData
): void => {
  if (isCalibrated(calibrationData)) {
    updateMeasurementText(canvas, line, calibrationData!);
    return;
  }
};

export const isMeasurementLine = (obj: any): obj is Line => {
  return (
    ((obj as any)?.name === "measurementLine" || (obj as any)?.customType === "measurementLine") &&
    (obj as any)?.type === "line"
  );
};

/*
Restores custom render method for measurements after loading from JSON.
Fabric.js doesn't serialize custom functions, so we need to reattach them.
*/
export const restoreMeasurementRender = (line: Line) => {
  const originalToObject = line.toObject;
  (line as any).toObject = function (propertiesToInclude?: any[]) {
    const customProps = ["measurementText", "customType", "id"];
    const allProps = propertiesToInclude ? [...propertiesToInclude, ...customProps] : customProps;
    return originalToObject.call(this, allProps as any);
  };

  const originalRender = line._render;
  (line as any)._render = function (ctx: CanvasRenderingContext2D) {
    originalRender.call(this, ctx);

    const text = (this as any).measurementText || "0.00 mm";
    const points = this.calcLinePoints();
    const x1 = points.x1;
    const y1 = points.y1;
    const x2 = points.x2;
    const y2 = points.y2;

    if (Math.abs(x2 - x1) < 1 && Math.abs(y2 - y1) < 1) return;

    const tipX = x1;
    const tipY = y1;

    ctx.save();
    ctx.translate(tipX, tipY);

    const baseSize = 20;
    const scaleX = this.scaleX || 1;
    const scaleY = this.scaleY || 1;

    ctx.scale(1 / scaleX, 1 / scaleY);

    ctx.fillStyle = this.stroke || "#ff0000";
    ctx.font = `${baseSize}px Arial`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    const dy = y2 - y1;
    const textDistance = 15;
    const offsetY = dy >= 0 ? -textDistance : textDistance;

    const textWidth = ctx.measureText(text).width;
    const bgPadding = 4;
    ctx.fillStyle = "rgba(255, 255, 255, 0.9)";
    ctx.fillRect(
      -textWidth / 2 - bgPadding,
      offsetY - baseSize / 2 - bgPadding,
      textWidth + bgPadding * 2,
      baseSize + bgPadding * 2
    );

    ctx.fillStyle = this.stroke || "#ff0000";
    ctx.fillText(text, 0, offsetY);
    ctx.restore();
  };
};
