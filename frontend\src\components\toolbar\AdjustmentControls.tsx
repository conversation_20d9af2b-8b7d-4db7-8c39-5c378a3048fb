import React from "react";
import { Button, ControlGroup, Icon, Slider, Tag } from "@blueprintjs/core";
import type { SliderControlsProps } from "@/models";

const SliderControls: React.FC<SliderControlsProps> = ({
  brightness,
  contrast,
  sharpness,
  onBrightnessChange,
  onContrastChange,
  onSharpnessChange,
}) => {
  const sliders = [
    {
      icon: "flash",
      label: "Bright",
      value: brightness,
      onChange: onBrightnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
      def: 1,
    },
    {
      icon: "contrast",
      label: "Contrast",
      value: contrast,
      onChange: onContrastChange,
      min: 0.1,
      max: 2,
      step: 0.1,
      def: 1,
    },
    {
      icon: "zoom-in",
      label: "Sharpness",
      value: sharpness,
      onChange: onSharpnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
      def: 1,
    },
  ] as const;

  return (
    <>
      {sliders.map((s) => (
        <ControlGroup
          key={s.label}
          fill
          className="bp5-condensed"
          style={{ alignItems: "center", gap: 4 }}
        >
          <Icon icon={s.icon} title={s.label} />
          <Slider
            className="bp5-small"
            min={s.min}
            max={s.max}
            stepSize={s.step}
            labelRenderer={false}
            value={s.value}
            onChange={s.onChange}
          />
          <Tag minimal>{(s.value || 0).toFixed(1)}</Tag>
          <Button
            className="bp5-small bp5-minimal"
            icon="reset"
            onClick={() => s.onChange(s.def)}
            aria-label={`Reset ${s.label}`}
          />
        </ControlGroup>
      ))}
    </>
  );
};

export default SliderControls;
